import { ApplicationScheduleType } from './api.gen';

export enum ConsumerLoanProduct {
  FAST_LOAN = ApplicationScheduleType.FAST_LOAN,
  RENOVATION_LOAN = ApplicationScheduleType.RENOVATION_LOAN,
  SMALL_LOAN = ApplicationScheduleType.SMALL_LOAN,
  VEHICLE_LOAN = ApplicationScheduleType.VEHICLE_LOAN,
  TRAVEL_LOAN = ApplicationScheduleType.TRAVEL_LOAN,
  HEALTH_LOAN = ApplicationScheduleType.HEALTH_LOAN,
  BEAUTY_LOAN = ApplicationScheduleType.BEAUTY_LOAN,
}

export enum HirePurchaseProduct {
  ESTO_PAY = ApplicationScheduleType.ESTO_PAY,
  ESTO_X = ApplicationScheduleType.ESTO_X,
  PAY_LATER = ApplicationScheduleType.PAY_LATER,
  REGULAR = ApplicationScheduleType.REGULAR,
}

export enum NonLoanProduct {
  CREDIT_LINE = 'CREDIT_LINE',
  PREMIUM_SUBSCRIPTION = 'PREMIUM_SUBSCRIPTION',
}

export type LoanProduct = ConsumerLoanProduct | HirePurchaseProduct;

export type EstoProductType = NonLoanProduct.CREDIT_LINE | ConsumerLoanProduct;

export type ProductType =
  | NonLoanProduct
  | ConsumerLoanProduct
  | HirePurchaseProduct;

export enum PremiumSubscriptionStatus {
  NO_ACTIVE_SUBSCRIPTION = 'NO_ACTIVE_SUBSCRIPTION',
  ACTIVE_SUSPENDED = 'ACTIVE_SUSPENDED',
  ACTIVE_UNSUBSCRIBED = 'ACTIVE_UNSUBSCRIBED',
  ACTIVE_RENEW = 'ACTIVE_RENEW',
}
