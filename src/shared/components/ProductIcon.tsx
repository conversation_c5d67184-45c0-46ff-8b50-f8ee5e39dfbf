import PremiumSubscriptionIcon from '@icons/crown.svg?react';
import BeautyLoanIcon from '@icons/products/beauty-loan.svg?react';
import CreditLineIcon from '@icons/products/credit-line.svg?react';
import FastLoanIcon from '@icons/products/fast-loan.svg?react';
import HealthLoanIcon from '@icons/products/health-loan.svg?react';
import HirePurchaseIcon from '@icons/products/hire-purchase.svg?react';
import RenovationLoanIcon from '@icons/products/renovation-loan.svg?react';
import SmallLoanIcon from '@icons/products/small-loan.svg?react';
import TravelLoanIcon from '@icons/products/traver-loan.svg?react';
import VehicleLoanIcon from '@icons/products/vehicle-loan.svg?react';
import { cva, type VariantProps } from 'class-variance-authority';
import { type ElementType, useState } from 'react';

import {
  ConsumerLoanProduct,
  HirePurchaseProduct,
  NonLoanProduct,
  type ProductType,
} from '@/shared/types';

import { cn } from '../utils/tailwind';

const PRODUCT_ICON_BY_PRODUCT_TYPE: Record<ProductType, ElementType> = {
  [HirePurchaseProduct.REGULAR]: HirePurchaseIcon,
  [HirePurchaseProduct.ESTO_PAY]: HirePurchaseIcon,
  [HirePurchaseProduct.ESTO_X]: HirePurchaseIcon,
  [HirePurchaseProduct.PAY_LATER]: HirePurchaseIcon,
  [ConsumerLoanProduct.FAST_LOAN]: FastLoanIcon,
  [ConsumerLoanProduct.RENOVATION_LOAN]: RenovationLoanIcon,
  [ConsumerLoanProduct.VEHICLE_LOAN]: VehicleLoanIcon,
  [ConsumerLoanProduct.SMALL_LOAN]: SmallLoanIcon,
  [ConsumerLoanProduct.TRAVEL_LOAN]: TravelLoanIcon,
  [ConsumerLoanProduct.HEALTH_LOAN]: HealthLoanIcon,
  [ConsumerLoanProduct.BEAUTY_LOAN]: BeautyLoanIcon,
  [NonLoanProduct.CREDIT_LINE]: CreditLineIcon,
  [NonLoanProduct.PREMIUM_SUBSCRIPTION]: PremiumSubscriptionIcon,
};

export const productIconVariants = cva(
  'flex items-center justify-center overflow-hidden rounded-full bg-primary-black [&>svg]:text-primary-white',
  {
    variants: {
      size: {
        small: 'size-12 [&>svg]:size-[1.125rem]',
        medium: 'size-16 [&>svg]:size-6',
        large: 'size-[4.5rem] [&>svg]:size-7',
      },
    },
    defaultVariants: {
      size: 'medium',
    },
  },
);

export type ProductIconProps = VariantProps<typeof productIconVariants> & {
  productType: keyof typeof PRODUCT_ICON_BY_PRODUCT_TYPE;
  merchantLogoSrc?: Nullable<string>;
  className?: string;
};

export const ProductIcon = ({
  productType,
  merchantLogoSrc,
  className,
  size = 'medium',
}: ProductIconProps) => {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const Icon = PRODUCT_ICON_BY_PRODUCT_TYPE[productType];
  const classNames = productIconVariants({
    size,
    className,
  });

  if (merchantLogoSrc) {
    return (
      <div
        className={cn(
          classNames,
          isImageLoaded
            ? 'border border-neutral-200 bg-primary-white'
            : 'bg-primary-black',
        )}
      >
        {!isImageLoaded ? <Icon /> : null}
        <img
          src={merchantLogoSrc}
          onLoad={() => setIsImageLoaded(true)}
          alt={productType}
          className={cn('size-full object-contain', !isImageLoaded && 'hidden')}
        />
      </div>
    );
  }

  return (
    <div className={classNames}>
      <Icon />
    </div>
  );
};
