import type { TypographyProps } from '@components/typography';
import { Typography } from '@components/typography';
import { cn } from '@utils/tailwind';
import type { FC } from 'react';

type ActionCardProps = {
  title: string;
  subtitle?: string;
  description: string;
  before?: React.ReactNode;
  after?: React.ReactNode;
  classNames?: {
    titleVariant?: TypographyProps['variant'];
    title?: string;
    subtitle?: string;
  };
  icon?: React.ReactNode;
};

export const ActionCard: FC<ActionCardProps> = ({
  title,
  subtitle,
  description,
  before,
  after,
  classNames,
  icon,
}) => {
  return (
    <div className="mx-auto flex w-full max-w-[25rem] flex-col items-center">
      {icon && icon}
      <div className="grid gap-6 text-center mt-6 mb-14">
        <Typography
          variant={classNames?.titleVariant ? classNames.titleVariant : 'm'}
          className={cn(classNames?.title)}
        >
          {title}
        </Typography>
        {subtitle && (
          <Typography variant="m" className={cn(classNames?.subtitle)}>
            {subtitle}
          </Typography>
        )}
        <Typography affects="semibold" variant="s">
          {description}
        </Typography>
        {before && before}
      </div>
      {after}
    </div>
  );
};
