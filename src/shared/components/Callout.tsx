import { Typography } from '@components/typography';
import { cn } from '@utils/tailwind';
import type { FC, ReactNode } from 'react';

type CalloutProps = {
  text: string | ReactNode;
  className?: string;
};

export const Callout: FC<CalloutProps> = ({ text, className }) => (
  <div
    className={cn(
      'grid grid-cols-[auto_1fr] gap-4 rounded-2xl bg-neutral-50 p-4',
      className,
    )}
  >
    <span>💡</span>
    <Typography>{text}</Typography>
  </div>
);
