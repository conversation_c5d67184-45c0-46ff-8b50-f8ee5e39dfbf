import { ProductIcon } from '@components/ProductIcon';
import { Typography } from '@components/typography';
import { Separator } from '@components/ui/separator';
import type { ReactNode } from 'react';

import type { Merchant, ProductType } from '@/shared/types';

type LoanInfoCardProps = {
  before?: ReactNode;
  after?: ReactNode;
  title: string;
  merchantLogoSrc?: Merchant['logo_path'];
  productType: ProductType;
};

export const LoanInfoCard = ({
  before,
  after,
  title,
  merchantLogoSrc,
  productType,
}: LoanInfoCardProps) => {
  return (
    <div className="flex flex-1 gap-4">
      <div className="-mt-9 w-full relative flex flex-col rounded-3xl border border-neutral-200 bg-primary-white p-8">
        <div className="absolute top-2 left-1/2 -translate-x-1/2 -translate-y-1/2">
          <ProductIcon
            merchantLogoSrc={merchantLogoSrc}
            size="medium"
            productType={productType}
          />
        </div>
        <div className="pt-6 grid gap-6">
          <div className="flex flex-col items-center gap-2 text-center">
            <Typography variant="xs">{title}</Typography>
            {before}
          </div>
          <Separator orientation="horizontal" decorative />
        </div>
        {after}
      </div>
    </div>
  );
};
