import {
  Carousel,
  type Carousel<PERSON>pi,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@components/ui/carousel';
import { useIsMobileView } from '@hooks/system';
import { useIntersectionObserver } from '@hooks/system/use-intersection-observer';
import { cn } from '@utils/tailwind';
import { uniqBy } from 'lodash';
import { useEffect, useRef } from 'react';

type DealsFeatureCarouselProps<T extends { id?: string | number }> = {
  cards: T[];
  title: React.ReactNode;
  actionBtn?: React.ReactNode;
  renderCard: (card: T) => React.ReactNode;
  hasNextPage?: boolean;
  fetchNextPage?: () => void; // Callback to fetch more items
  isFetchingNextPage?: boolean;
};

export const CardsCarousel = <T extends { id?: string | number }>({
  title,
  cards,
  actionBtn,
  renderCard,
  hasNextPage,
  fetchNextPage,
  isFetchingNextPage,
}: DealsFeatureCarouselProps<T>) => {
  const isMobileView = useIsMobileView();
  const carouselApiRef = useRef<CarouselApi>();
  const selectedIndexRef = useRef<number>(0);

  const { ref: triggerRef, isIntersecting } = useIntersectionObserver({
    rootMargin: '50px',
    threshold: 0.01,
  });

  useEffect(() => {
    const api = carouselApiRef.current;
    if (!api) return;

    const updateSelectedIndex = () => {
      selectedIndexRef.current = api.selectedScrollSnap();
    };

    api.on('select', updateSelectedIndex);
    api.on('reInit', updateSelectedIndex);

    updateSelectedIndex();

    return () => {
      api.off('select', updateSelectedIndex);
      api.off('reInit', updateSelectedIndex);
    };
  }, [carouselApiRef.current]);

  useEffect(() => {
    if (isIntersecting && hasNextPage && !isFetchingNextPage) {
      fetchNextPage?.();
    }
  }, [isIntersecting, hasNextPage, isFetchingNextPage, fetchNextPage]);

  if (!cards?.length) {
    return null;
  }

  const finalCards = uniqBy(cards, 'id');

  return (
    <Carousel
      className={'w-full overflow-hidden'}
      opts={{
        loop: false,
        align: 'start',
        dragFree: true,
        duration: 20,
        watchSlides: (api, _mutations) => {
          const currentIndex = selectedIndexRef.current;

          requestAnimationFrame(() => {
            if (api && currentIndex >= 0) {
              api.scrollTo(currentIndex, true);
            }
          });

          return true;
        },
      }}
      setApi={(api) => {
        carouselApiRef.current = api;
      }}
    >
      <div
        className={'mb-6 md:mb-2 grid grid-flow-col items-center px-6 md:px-0'}
      >
        {title}

        <div className={'relative ml-auto h-8'}>
          {actionBtn}

          {!isMobileView && (
            <>
              <CarouselPrevious
                className={cn(
                  actionBtn ? '!-left-[5.7rem]' : '!-left-[4.7rem]',
                  '!absolute !top-0 !translate-y-0 border-none bg-neutral-50 p-1 disabled:hover:bg-neutral-50',
                )}
              />
              <CarouselNext
                className={cn(
                  actionBtn ? '!-left-[3rem]' : '!-left-[2rem]',
                  '!absolute !top-0 !translate-y-0 border-none bg-neutral-50 p-1 disabled:hover:bg-neutral-50',
                )}
              />
            </>
          )}
        </div>
      </div>
      <CarouselContent
        isInteractive
        containerClassName="pl-6 md:pl-0"
        className="-ml-6 mr-5 flex py-4 transition-all duration-300 ease-out"
      >
        {finalCards.map((card, index) => (
          <CarouselItem
            key={card.id ? `card-${card.id}` : `card-index-${index}`}
            className="basis-[15.75rem] pl-6 transition-opacity duration-300 ease-in-out"
          >
            {renderCard(card)}
          </CarouselItem>
        ))}

        {hasNextPage && (
          <CarouselItem key="loading-trigger" className="basis-auto pl-6">
            <div
              ref={triggerRef}
              className="flex h-full w-16 items-center justify-center"
              style={{
                minWidth: '64px',
                visibility: 'visible',
                display: 'flex',
              }}
              data-testid="horizontal-infinite-scroll-trigger"
            >
              {isFetchingNextPage && (
                <div className="h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
              )}
            </div>
          </CarouselItem>
        )}
      </CarouselContent>
    </Carousel>
  );
};
