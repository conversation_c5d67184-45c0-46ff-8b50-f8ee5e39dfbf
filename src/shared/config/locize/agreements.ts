export const LOCIZE_AGREEMENTS_KEYS = {
  productConsumerLoanDescription: 'product.consumer-loan.description',
  productSmallLoanTitle: 'product.small-loan.title',
  productFastLoanTitle: 'product.fast-loan.title',
  productRenovationLoanTitle: 'product.renovation-loan.title',
  productVehicleLoanTitle: 'product.vehicle-loan.title',
  productTravelLoanTitle: 'product.travel-loan.title',
  productHealthLoanTitle: 'product.health-loan.title',
  productBeautyLoanTitle: 'product.beauty-loan.title',
  productEstoPayTitle: 'product.esto-pay.title',
  productEstoXTitle: 'product.esto-x.title',
  productRegularTitle: 'product.regular.title',
  productPayLaterTitle: 'product.pay-later.title',
  reduceMonthlyPayment: 'reduce-monthly-payment',
  reduceMonthlyPaymentNotification: 'notification.reduce-monthly-payment',
  creditLineNotification: 'notification.credit-line',
  dashboardTitle: 'dashboard.title',
  modalReduceMonthlyPayment: 'modal.reduce-monthly-payment',
  modalMakePayment: 'modal.make-payment',
  modalPaymentSchedule: 'modal.payment-schedule',
  modalTableTotal: 'modal.table.total',
  modalTableMonthlyPayment: 'modal.table.monthly-payment',
  modalTablePaid: 'modal.table.paid',
  modalTableSigned: 'modal.table.signed',
  modalTableAmount: 'modal.table.amount',
  modalTableContracts: 'modal.table.contracts',
  notificationReduceMonthlyPayment: 'notification.reduce-monthly-payment',
  none: 'none',
  loadMore: 'load-more',
  pageTitle: 'page-title',
  pastTitle: 'past.title',
  pastTableAmount: 'past.table.amount',
  pastTableAgreement: 'past.table.agreement',
  pastTableView: 'past.table.view',
  confirmModalTitle: 'confirm-modal.title',
  confirmModalDescription: 'confirm-modal.description',
  confirmModalConfirmBtn: 'confirm-modal.confirm-btn',
  confirmModalInvoicesBtn: 'confirm-modal.invoices-btn',
  successTitle: 'success.title',
  successDescription: 'success.description',
  rejectDescription: 'reject.description',
  rejectTitle: 'reject.title',
  agreementsTableDateCell: 'agreements-table.date-cell',
  agreementsTableToPayCell: 'agreements-table.to-pay-cell',
  agreementsTablePaidCell: 'agreements-table.paid-cell',
  agreementsTableTypeCell: 'agreements-table.type-cell',
  installmentTypeMonthly: 'agreements-table.installment-type.monthly',
  installmentTypeDown: 'agreements-table.installment-type.down',
} as const;
