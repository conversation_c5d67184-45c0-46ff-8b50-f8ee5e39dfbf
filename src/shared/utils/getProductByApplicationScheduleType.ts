import {
  ApplicationScheduleType,
  ConsumerLoanProduct,
  HirePurchaseProduct,
  type LoanProduct,
} from '@/shared/types';

const productByScheduleType: Record<ApplicationScheduleType, LoanProduct> = {
  [ApplicationScheduleType.ESTO_PAY]: HirePurchaseProduct.ESTO_PAY,
  [ApplicationScheduleType.ESTO_X]: HirePurchaseProduct.ESTO_X,
  [ApplicationScheduleType.PAY_LATER]: HirePurchaseProduct.PAY_LATER,
  [ApplicationScheduleType.REGULAR]: HirePurchaseProduct.REGULAR,
  [ApplicationScheduleType.FAST_LOAN]: ConsumerLoanProduct.FAST_LOAN,
  [ApplicationScheduleType.RENOVATION_LOAN]:
    ConsumerLoanProduct.RENOVATION_LOAN,
  [ApplicationScheduleType.SMALL_LOAN]: ConsumerLoanProduct.SMALL_LOAN,
  [ApplicationScheduleType.VEHICLE_LOAN]: ConsumerLoanProduct.VEHICLE_LOAN,
  [ApplicationScheduleType.TRAVEL_LOAN]: ConsumerLoanProduct.TRAVEL_LOAN,
  [ApplicationScheduleType.HEALTH_LOAN]: ConsumerLoanProduct.HEALTH_LOAN,
  [ApplicationScheduleType.BEAUTY_LOAN]: ConsumerLoanProduct.BEAUTY_LOAN,
};

export const getProductByScheduleType = (
  scheduleType: ApplicationScheduleType,
): LoanProduct => {
  return productByScheduleType[scheduleType];
};
