import {
  ApplicationScheduleType,
  ConsumerLoanProduct,
  HirePurchaseProduct,
  type LoanProduct,
} from '@/shared/types';

const applicationScheduleTypeByProduct = {
  [HirePurchaseProduct.ESTO_PAY]: ApplicationScheduleType.ESTO_PAY,
  [HirePurchaseProduct.ESTO_X]: ApplicationScheduleType.ESTO_X,
  [HirePurchaseProduct.PAY_LATER]: ApplicationScheduleType.PAY_LATER,
  [HirePurchaseProduct.REGULAR]: ApplicationScheduleType.REGULAR,
  [ConsumerLoanProduct.FAST_LOAN]: ApplicationScheduleType.FAST_LOAN,
  [ConsumerLoanProduct.RENOVATION_LOAN]:
    ApplicationScheduleType.RENOVATION_LOAN,
  [ConsumerLoanProduct.SMALL_LOAN]: ApplicationScheduleType.SMALL_LOAN,
  [ConsumerLoanProduct.VEHICLE_LOAN]: ApplicationScheduleType.VEHICLE_LOAN,
  [ConsumerLoanProduct.TRAVEL_LOAN]: ApplicationScheduleType.TRAVEL_LOAN,
  [ConsumerLoanProduct.HEALTH_LOAN]: ApplicationScheduleType.HEALTH_LOAN,
  [ConsumerLoanProduct.BEAUTY_LOAN]: ApplicationScheduleType.BEAUTY_LOAN,
};

export const getApplicationScheduleTypeByProduct = (
  product: LoanProduct,
): ApplicationScheduleType => {
  return applicationScheduleTypeByProduct[product];
};
