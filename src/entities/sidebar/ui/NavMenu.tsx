import { Separator } from '@components/ui/separator';
import { cn } from '@utils/tailwind';
import { Fragment } from 'react';

import { SIDEBAR_TEST_KEYS } from '../config';
import type { NavLinkType } from '../types';
import { NavLink } from './NavLink';

type NavMenuProps = {
  items: Array<NavLinkType>;
  className?: string;
};

export const NavMenu = ({ items, className }: NavMenuProps) => (
  <nav
    className={cn('grid gap-3', className)}
    data-testid={SIDEBAR_TEST_KEYS.navMenu}
  >
    {items.map((item) => {
      if (item.withSeparator) {
        return (
          <Fragment key={item.title}>
            <NavLink {...item} />
            <Separator className="my-2" orientation="horizontal" decorative />
          </Fragment>
        );
      }
      return <NavLink key={item.title} {...item} />;
    })}
  </nav>
);
