import { ProductIcon } from '@components/ProductIcon';
import { Typography } from '@components/typography';
import { LOCIZE_NAMESPACES, LOCIZE_OFFERS_KEYS } from '@config/locize';
import CheckBoldIcon from '@icons/check-bold.svg?react';
import { cn } from '@utils/tailwind';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { NOISE_TEXTURE_STYLES } from '@/shared/config/styles';
import { type EstoProductType, NonLoanProduct } from '@/shared/types';

export type OfferCardProps = {
  img: string;
  title: string;
  description: Array<string>;
  productType: EstoProductType;
  className?: string;
  isStarProduct?: boolean;
};

export const OfferCard: FC<OfferCardProps> = ({
  img,
  title,
  description,
  productType,
  className,
  isStarProduct,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.offers);

  return (
    <div
      key={productType}
      className={cn(
        'relative flex flex-col rounded-2xl bg-primary-white overflow-hidden group',
        className,
      )}
    >
      {isStarProduct && (
        <div className="absolute top-0 right-0 flex h-7 w-40 z-10 translate-x-11 translate-y-6 rotate-45 items-center justify-center bg-system-green2">
          <Typography
            variant="text-s"
            tag="span"
            className="text-primary-white"
          >
            {t(LOCIZE_OFFERS_KEYS.starProductLabel)}
          </Typography>
        </div>
      )}

      <div className="overflow-hidden rounded-2xl">
        <div
          style={{
            backgroundImage: `url(${img})`,
          }}
          className="h-[16rem] transition-transform duration-300 ease-in-out group-hover:scale-110 bg-center bg-cover"
        />
        <div
          className="absolute top-0 left-0 h-full w-full rounded-2xl"
          style={NOISE_TEXTURE_STYLES}
        />
      </div>

      <div className="grid gap-[.875rem] py-6 relative">
        <div className="bg-primary-white absolute -top-6 right-4 p-0.5 w-12 h-12 rounded-full">
          <ProductIcon
            className={cn(
              'w-full h-full',
              productType === NonLoanProduct.CREDIT_LINE && 'bg-system-green2',
            )}
            productType={productType}
            size="small"
          />
        </div>
        <div className="grid gap-3">
          <Typography variant="text-xl" affects="bold">
            {title}
          </Typography>
          <div className="grid gap-1.5">
            {description.map((desc) => (
              <div key={desc} className="flex items-center gap-2">
                <CheckBoldIcon className={'text-primary-green'} />
                <Typography key={desc} tag="span" variant="text-m">
                  {desc}
                </Typography>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
