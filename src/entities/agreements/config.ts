import { LOCIZE_AGREEMENTS_KEYS } from '@config/locize';

import { ApplicationScheduleType } from '@/shared/types';

export const AGREEMENT_CONFIG_BY_SCHEDULE_TYPE: Readonly<
  Record<
    ApplicationScheduleType,
    {
      title: string;
      description?: string;
    }
  >
> = {
  [ApplicationScheduleType.SMALL_LOAN]: {
    title: LOCIZE_AGREEMENTS_KEYS.productSmallLoanTitle,
    description: LOCIZE_AGREEMENTS_KEYS.productConsumerLoanDescription,
  },
  [ApplicationScheduleType.FAST_LOAN]: {
    title: LOCIZE_AGREEMENTS_KEYS.productFastLoanTitle,
    description: LOCIZE_AGREEMENTS_KEYS.productConsumerLoanDescription,
  },
  [ApplicationScheduleType.RENOVATION_LOAN]: {
    title: LOCIZE_AGREEMENTS_KEYS.productRenovationLoanTitle,
    description: LOCIZE_AGREEMENTS_KEYS.productConsumerLoanDescription,
  },
  [ApplicationScheduleType.VEHICLE_LOAN]: {
    title: LOCIZE_AGREEMENTS_KEYS.productVehicleLoanTitle,
    description: LOCIZE_AGREEMENTS_KEYS.productConsumerLoanDescription,
  },
  [ApplicationScheduleType.TRAVEL_LOAN]: {
    title: LOCIZE_AGREEMENTS_KEYS.productTravelLoanTitle,
    description: LOCIZE_AGREEMENTS_KEYS.productConsumerLoanDescription,
  },
  [ApplicationScheduleType.HEALTH_LOAN]: {
    title: LOCIZE_AGREEMENTS_KEYS.productHealthLoanTitle,
  },
  [ApplicationScheduleType.BEAUTY_LOAN]: {
    title: LOCIZE_AGREEMENTS_KEYS.productBeautyLoanTitle,
    description: LOCIZE_AGREEMENTS_KEYS.productConsumerLoanDescription,
  },
  [ApplicationScheduleType.REGULAR]: {
    title: LOCIZE_AGREEMENTS_KEYS.productRegularTitle,
  },
  [ApplicationScheduleType.PAY_LATER]: {
    title: LOCIZE_AGREEMENTS_KEYS.productPayLaterTitle,
  },
  [ApplicationScheduleType.ESTO_PAY]: {
    title: LOCIZE_AGREEMENTS_KEYS.productEstoPayTitle,
  },
  [ApplicationScheduleType.ESTO_X]: {
    title: LOCIZE_AGREEMENTS_KEYS.productEstoXTitle,
  },
};
