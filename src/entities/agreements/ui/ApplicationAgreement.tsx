import { ProductIcon, productIconVariants } from '@components/ProductIcon';
import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { Progress } from '@components/ui/progress';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_AGREEMENTS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { OLD_APP_ROUTE_NAME, ROUTE_NAMES } from '@config/routes';
import { AGREEMENT_CONFIG_BY_SCHEDULE_TYPE } from '@entities/agreements/config';
import { useFeatureToggles } from '@hooks/system';
import { Link, useNavigate } from '@tanstack/react-router';
import { getProductByScheduleType } from '@utils/getProductByApplicationScheduleType';
import { getProportion } from '@utils/math/getProportion';
import { cn } from '@utils/tailwind';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import {
  type Application,
  ApplicationScheduleType,
  type Merchant,
  type MerchantCampaign,
} from '@/shared/types';

type ApplicationAgreementProps = {
  unpaidPrincipal: Application['unpaid_principal'];
  requestedAmount: Application['requested_amount'];
  scheduleType: Application['schedule_type'];
  merchantName?: Merchant['name'];
  merchantLogoSrc: Merchant['logo_path'];
  convertingScheduleName?: MerchantCampaign['converting_schedule_name'];
  canManuallyConvertToCreditAccount: Application['can_manually_convert_to_credit_account'];
  referenceKey: Application['reference_key'];
};

export const ApplicationAgreement: FC<ApplicationAgreementProps> = ({
  merchantName,
  merchantLogoSrc,
  requestedAmount,
  scheduleType,
  unpaidPrincipal,
  canManuallyConvertToCreditAccount,
  referenceKey,
  convertingScheduleName,
}) => {
  const navigate = useNavigate();

  const { creditAccountConversionFeature } = useFeatureToggles();

  const { t } = useTranslation(LOCIZE_NAMESPACES.agreements);

  const { title, description } =
    AGREEMENT_CONFIG_BY_SCHEDULE_TYPE[scheduleType];

  const agreementDescription = merchantName ?? (description && t(description));

  const onReduceMonthlyPaymentClick = () => {
    navigate({
      to: ROUTE_NAMES.creditAccountConversionApply,
      search: {
        referenceKey,
        fromPathname: location.pathname,
      },
    });
  };

  return (
    <div className={cn('relative', 'group')}>
      <Link
        to={ROUTE_NAMES.current}
        search={(prev) => ({
          ...prev,
          agreementReferenceKey: referenceKey,
        })}
        className={cn(
          'grid border border-neutral-200 rounded-2xl transition-shadow overflow-hidden',
          'group-hover:shadow-hover',
        )}
      >
        <div
          className={cn(
            'flex items-center gap-x-4 p-4',
            canManuallyConvertToCreditAccount && '!pr-[7.625rem]',
          )}
        >
          <ProductIcon
            merchantLogoSrc={merchantLogoSrc}
            productType={getProductByScheduleType(scheduleType)}
            size="small"
          />
          <div className="grid gap-1">
            <Typography variant="text-l" affects="semibold">
              {scheduleType === ApplicationScheduleType.ESTO_X
                ? convertingScheduleName
                : t(title)}
            </Typography>
            {agreementDescription ? (
              <Typography variant="text-s" className="text-neutral-500">
                {agreementDescription}
              </Typography>
            ) : null}
          </div>
        </div>
        <Progress
          variant="blue"
          value={
            getProportion({
              min: 0,
              max: requestedAmount,
              value: unpaidPrincipal ? requestedAmount - unpaidPrincipal : 0,
            }) * 100
          }
        />
      </Link>
      {canManuallyConvertToCreditAccount ? (
        creditAccountConversionFeature ? (
          <Button
            onClick={onReduceMonthlyPaymentClick}
            size="small"
            className="absolute top-6 right-4"
          >
            {t(LOCIZE_AGREEMENTS_KEYS.reduceMonthlyPayment)}
          </Button>
        ) : (
          <Button size="small" asChild className="absolute top-6 right-4">
            <a
              href={OLD_APP_ROUTE_NAME.applicationReduceMonthlyPayment.replace(
                ':applicationReferenceKey',
                referenceKey,
              )}
            >
              {t(LOCIZE_AGREEMENTS_KEYS.reduceMonthlyPayment)}
            </a>
          </Button>
        )
      ) : null}
    </div>
  );
};

export const ApplicationAgreementSkeleton = () => (
  <Skeleton className="grid border border-neutral-200 rounded-2xl transition-shadow overflow-hidden">
    <div className="flex items-center gap-x-4 p-4">
      <Skeleton
        className={productIconVariants({
          size: 'small',
          className: 'bg-neutral-200',
        })}
      />
      <div className={cn('grid gap-1')}>
        <Skeleton className="h-[1.5rem] w-24 bg-neutral-200" />
        <Skeleton className="h-[1.25rem] w-24 bg-neutral-200" />
      </div>
    </div>
    <Skeleton className="h-1.5 w-full bg-neutral-200" />
  </Skeleton>
);
