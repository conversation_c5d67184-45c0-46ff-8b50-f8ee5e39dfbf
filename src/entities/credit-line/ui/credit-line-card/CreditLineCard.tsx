import { ProgressBarWithLabel } from '@components/ProgressBarWithLabel';
import { Typography } from '@components/typography';
import { progressVariants } from '@components/ui/progress';
import { Skeleton } from '@components/ui/skeleton';
import {
  LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { formatNumber } from '@utils/formatters';
import { cn } from '@utils/tailwind';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import type { CreditAccount } from '@/shared/types';

import { WITHDRAWAL_PANEL_TEST_KEYS } from '../../config';

export type CreditLineCardProps = {
  creditLimit: CreditAccount['credit_limit'];
  unpaidPrincipal: CreditAccount['unpaid_principal'];
  after?: React.ReactNode;
  title?: string;
  ModificationCtaButton?: React.ReactNode;
  LimitIncreaseCtaButton?: React.ReactNode;
};

export const CreditLineCard: FC<CreditLineCardProps> = ({
  creditLimit,
  unpaidPrincipal,
  after,
  title,
  ModificationCtaButton,
  LimitIncreaseCtaButton,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditLineBalanceWidget);

  const allowedPrincipal = creditLimit - unpaidPrincipal;

  const cardTitle = title ?? t(LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.title);

  return (
    <div
      className="grid grid-areas-[info,progress,after] data-[with-cta=true]:grid-areas-[info,progress,after,ctaContainer] grid-cols-1 items-center gap-x-4 gap-y-6 pb-[4.5rem] bg-primary-white md:grid-cols-[1fr,auto] md:rounded-3xl md:border md:border-neutral-200 md:p-8 md:grid-areas-[info_after,progress_progress] md:data-[with-cta=true]:grid-areas-[info_after,progress_progress,ctaContainer_ctaContainer]"
      data-with-cta={!!ModificationCtaButton || !!LimitIncreaseCtaButton}
      data-testid={WITHDRAWAL_PANEL_TEST_KEYS.container}
    >
      <div className="grid-in-[info] grid gap-1.5">
        <Typography
          testId={WITHDRAWAL_PANEL_TEST_KEYS.balance}
          variant="text-s"
        >
          {cardTitle}
        </Typography>
        <Typography
          testId={WITHDRAWAL_PANEL_TEST_KEYS.balanceValue}
          variant="m"
        >
          {Math.floor(allowedPrincipal).toLocaleString()} €
        </Typography>
      </div>
      <ProgressBarWithLabel
        maxValue={creditLimit}
        minValue={0}
        testId={WITHDRAWAL_PANEL_TEST_KEYS.progressBar}
        value={allowedPrincipal}
        maxLabel={`${formatNumber({ value: creditLimit })} €`}
        className="grid-in-[progress]"
        minLabel={t(
          LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.signedCaAmountMinLabel,
        )}
      />
      {after && <div className="grid-in-[after] mt-2 md:mt-0">{after}</div>}
      {ModificationCtaButton || LimitIncreaseCtaButton ? (
        <div className="grid gap-2 md:col-span-2">
          {LimitIncreaseCtaButton && LimitIncreaseCtaButton}
          {ModificationCtaButton && ModificationCtaButton}
        </div>
      ) : null}
    </div>
  );
};

export const CreditLineCardSkeleton = () => (
  <Skeleton
    className={cn(
      'grid grid-areas-[info,progress,after] data-[with-cta=true]:grid-areas-[info,progress,after,ctaContainer] grid-cols-1 items-center gap-x-4 gap-y-6 pb-[4.5rem] bg-primary-white md:grid-cols-[1fr,auto] md:rounded-3xl md:border md:border-neutral-200 md:p-8 md:grid-areas-[info_after,progress_progress] md:data-[with-cta=true]:grid-areas-[info_after,progress_progress,ctaContainer_ctaContainer]',
      'rounded-3xl border-none bg-primary-white px-6 py-8 md:bg-muted md:p-8 md:pb-14',
    )}
  >
    <div className="grid-in-[info] grid gap-1.5">
      <Skeleton className="h-[1.25rem] w-56 bg-neutral-200" />
      <Skeleton className="h-[2.5rem] w-36 bg-neutral-200" />
    </div>
    <Skeleton
      className={cn(
        'grid-in-[after] mt-2 md:mt-0',
        'h-12 w-full rounded-full bg-neutral-200 md:w-28',
      )}
    />
    <Skeleton
      className={cn(
        'grid-in-[progress]',
        progressVariants({
          className: 'bg-gray-300',
        }),
      )}
    />
  </Skeleton>
);
