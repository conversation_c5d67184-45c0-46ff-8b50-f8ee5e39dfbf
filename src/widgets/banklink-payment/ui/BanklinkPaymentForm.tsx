import { Button } from '@components/ui/button';
import { LOCIZE_NAMESPACES, LOCIZE_PAYMENT_KEYS } from '@config/locize';
import { BanklinkMethodsSelect } from '@features/banklink-methods-select';
import { useToast } from '@hooks/system';
import { banklinkPaymentApi } from '@widgets/banklink-payment/api';
import { type FC, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { CreateKlixPaymentMutationVariables } from '../api/mutations.gen';

type BanklinkPaymentFormProps = Omit<
  CreateKlixPaymentMutationVariables,
  'paymentMethodKey'
> & {
  onCancel?: () => void;
};

export const BanklinkPaymentForm: FC<BanklinkPaymentFormProps> = ({
  amount,
  redirectUrl,
  userId,
  applicationId,
  creditAccountId,
  onCancel,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.payment);

  const { showErrorMessage } = useToast();
  const [selectedPaymentMethodKey, setSelectedPaymentMethodKey] =
    useState<Nullable<string>>(null);

  const createBanklinkPaymentMutation =
    banklinkPaymentApi.useCreateKlixPaymentMutation({
      onSuccess: (data) => {
        if (!data?.payment?.payment_url) {
          // TODO: Update error message
          showErrorMessage('Something went wrong. Please try again later');

          return;
        }

        window.open(data.payment.payment_url, '_self');
      },
    });

  const onSubmit = () => {
    if (!selectedPaymentMethodKey)
      throw new Error('No payment method selected');

    createBanklinkPaymentMutation.mutate({
      amount,
      redirectUrl,
      userId,
      applicationId,
      creditAccountId,
      paymentMethodKey: selectedPaymentMethodKey,
    });
  };

  return (
    <div>
      <BanklinkMethodsSelect
        value={selectedPaymentMethodKey}
        onChange={setSelectedPaymentMethodKey}
      />
      <div className="grid gap-4 mt-14">
        <Button
          onClick={onSubmit}
          loading={createBanklinkPaymentMutation.isPending}
          disabled={!selectedPaymentMethodKey}
        >
          {t(LOCIZE_PAYMENT_KEYS.payButton)}
        </Button>
        {onCancel ? (
          <Button variant="white" onClick={onCancel}>
            {t(LOCIZE_PAYMENT_KEYS.backButton)}
          </Button>
        ) : null}
      </div>
    </div>
  );
};
