import { ProductIcon } from '@components/ProductIcon';
import { Typography } from '@components/typography';
import { Skeleton } from '@components/ui/skeleton';
import {
  SmallDialogContent,
  SmallDialogFooter,
  SmallDialogHeader,
} from '@components/ui/small-dialog';
import { LOCIZE_AGREEMENTS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { AGREEMENT_CONFIG_BY_SCHEDULE_TYPE } from '@entities/agreements/config';
import { applicationApi } from '@entities/application/api';
import { useToast } from '@hooks/system';
import DownloadIcon from '@icons/download.svg?react';
import { getContractDownloadLink } from '@pages/agreements/utils';
import { Navigate } from '@tanstack/react-router';
import { formatDate, formatNumber } from '@utils/formatters';
import { getProductByScheduleType } from '@utils/getProductByApplicationScheduleType';
import { cn } from '@utils/tailwind';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useUpdateEffect } from 'react-use';

import { ApplicationScheduleType } from '@/shared/types';

type PastAgreementDialogProps = {
  agreementReferenceKey: string;
};

export const PastAgreementDialog: FC<PastAgreementDialogProps> = ({
  agreementReferenceKey,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.agreements);
  const { data, isSuccess, isError, isFetching } =
    applicationApi.useApplicationQuery(
      {
        referenceKey: agreementReferenceKey,
      },
      {
        select: (data) => {
          if (!data?.application_by_reference) return null;

          const { schedule_type, merchant, requested_amount, signed_at, id } =
            data.application_by_reference;

          const { title, description } =
            AGREEMENT_CONFIG_BY_SCHEDULE_TYPE[schedule_type];

          const agreementInfo = [
            {
              id: 1,
              label: t(LOCIZE_AGREEMENTS_KEYS.modalTableAmount),
              value: `${formatNumber({ value: requested_amount, minimumFractionDigits: 2 })} €`,
            },
            {
              id: 2,
              label: t(LOCIZE_AGREEMENTS_KEYS.modalTableSigned),
              value: `${formatDate(Number(signed_at) * 1000)}`,
            },
            {
              id: 3,
              label: t(LOCIZE_AGREEMENTS_KEYS.modalTableContracts),
              value: (
                <>
                  <a
                    href={getContractDownloadLink(id, schedule_type)}
                    className="flex items-center gap-2 rounded-lg bg-neutral-100 px-1 py-[0.125rem] text-primary-black"
                  >
                    {id}
                    <DownloadIcon className="w-[0.875rem] text-primary-black" />
                  </a>
                </>
              ),
            },
          ];

          return {
            title:
              (schedule_type === ApplicationScheduleType.ESTO_X &&
                merchant?.campaign?.converting_schedule_name) ||
              title,
            description,
            agreementDescription:
              merchant?.name ?? (description && t(description)),
            agreementInfo,
            productType: getProductByScheduleType(schedule_type),
            id,
            merchant: {
              name: merchant?.name,
              logoSrc: merchant?.logo_path,
            },
          };
        },
      },
    );

  const { showErrorMessage } = useToast();

  const isInvalidApplication = (isSuccess && !data) || isError;

  useUpdateEffect(() => {
    if (isInvalidApplication) {
      showErrorMessage(
        isError ? 'Something went wrong' : 'Agreement not found',
      );
    }
  });

  if ((isSuccess && !data) || isError) {
    return (
      <Navigate
        to="."
        replace
        search={(prev) => ({ ...prev, agreementReferenceKey: undefined })}
      />
    );
  }

  return (
    <SmallDialogContent>
      {isFetching || !data ? (
        <PastAgreementDialogSkeleton />
      ) : (
        <>
          <SmallDialogHeader>
            <div className="flex items-center justify-between gap-2">
              <div className="flex flex-col gap-2 items-start">
                <Typography variant="s" affects="semibold">
                  {t(data.title)}
                </Typography>
                {data.agreementDescription ? (
                  <Typography variant="text-l" className="text-neutral-500">
                    {data.agreementDescription}
                  </Typography>
                ) : null}
              </div>
              <ProductIcon
                className="ml-4"
                merchantLogoSrc={data.merchant.logoSrc}
                productType={data.productType}
                size="large"
              />
            </div>
          </SmallDialogHeader>

          <div className={cn('mt-2 divide-y-[1px] px-4')}>
            {data.agreementInfo.map(({ label, value, id }) => (
              <div
                key={id}
                className="flex items-center justify-between gap-2 py-3 last:!border-b-[1px]"
              >
                <Typography variant="text-s">{label}</Typography>
                <Typography variant="text-s">{value}</Typography>
              </div>
            ))}
          </div>
          <SmallDialogFooter className="m-2 space-y-4" />
        </>
      )}
    </SmallDialogContent>
  );
};

function PastAgreementDialogSkeleton() {
  return (
    <>
      <SmallDialogHeader>
        <div className="flex w-full items-center justify-between gap-2">
          <div className="flex w-full flex-1 flex-col gap-2">
            <Skeleton className="h-8 w-full bg-gray-300" />
            <Skeleton className="h-6 w-full bg-gray-200" />
          </div>
          <Skeleton className="ml-2 h-[4.5rem] w-[4.5rem] rounded-full bg-gray-300" />
        </div>
      </SmallDialogHeader>

      <div className="mt-2 divide-y-[1px] px-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <Skeleton
            key={`agreement-skeleton-${index.toString()}`}
            className="flex h-11 items-center justify-between gap-2 rounded-none"
          />
        ))}
      </div>
      <SmallDialogFooter className="m-2 space-y-4" />
    </>
  );
}
