import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { Skeleton } from '@components/ui/skeleton';
import { SmallDialog } from '@components/ui/small-dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@components/ui/table';
import { LOCIZE_AGREEMENTS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { AGREEMENT_CONFIG_BY_SCHEDULE_TYPE } from '@entities/agreements/config';
import { usePastAgreements } from '@pages/agreements/hooks/usePastAgreements';
import { Link, useNavigate, useSearch } from '@tanstack/react-router';
import { formatNumber } from '@utils/formatters';
import c from 'clsx';
import { type FC, Fragment, lazy, Suspense } from 'react';
import { useTranslation } from 'react-i18next';

import { ApplicationScheduleType } from '@/shared/types';

const PastAgreementDialog = lazy(() =>
  import('../past-agreement-dialog').then((module) => ({
    default: module.PastAgreementDialog,
  })),
);

type PastAgreementsListProps = {
  className?: string;
};

export const PastAgreementsList: FC<PastAgreementsListProps> = ({
  className,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.agreements);

  const { pastAgreementReferenceKey } = useSearch({ strict: false });
  const navigate = useNavigate();

  const onModalOpenChange = (state: boolean) => {
    if (!state) {
      navigate({
        to: ROUTE_NAMES.current,
        replace: true,
        search: (prev) => ({
          ...prev,
          pastAgreementReferenceKey: undefined,
        }),
      });
    }
  };

  const {
    data,
    isPending,
    isSuccess,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  } = usePastAgreements();

  if (isSuccess && !data?.pages[0]?.user_applications?.data) {
    return null;
  }

  return (
    <div
      className={c(
        'flex flex-col gap-2 items-start px-6 pt-6 pb-12 md:p-0',
        className,
      )}
    >
      <Typography variant="xs" tag="h2" className="mb-4">
        {t(LOCIZE_AGREEMENTS_KEYS.pastTitle)}
      </Typography>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>
              {t(LOCIZE_AGREEMENTS_KEYS.pastTableAgreement)}
            </TableHead>
            <TableHead> {t(LOCIZE_AGREEMENTS_KEYS.pastTableAmount)}</TableHead>
            <TableHead className="w-5">
              {t(LOCIZE_AGREEMENTS_KEYS.pastTableView)}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isPending ? (
            <PastAgreementsTableBodySkeleton />
          ) : (
            data?.pages?.map((page, i) => (
              <Fragment key={`past-agreements-page-${i.toString()}`}>
                {page.user_applications?.data?.map((agreement) => {
                  if (!agreement) return null;

                  return (
                    <TableRow key={agreement.id}>
                      <TableCell>
                        {agreement.schedule_type ===
                        ApplicationScheduleType.ESTO_X
                          ? agreement.merchant?.campaign
                              ?.converting_schedule_name
                          : t(
                              AGREEMENT_CONFIG_BY_SCHEDULE_TYPE[
                                agreement.schedule_type
                              ].title,
                            )}
                      </TableCell>
                      <TableCell>{`${formatNumber({ value: agreement.requested_amount, minimumFractionDigits: 2 })} €`}</TableCell>
                      <TableCell>
                        <Link
                          to={ROUTE_NAMES.current}
                          className="underline hover:no-underline"
                          search={(prev) => ({
                            ...prev,
                            pastAgreementReferenceKey: agreement.reference_key,
                          })}
                        >
                          {t(LOCIZE_AGREEMENTS_KEYS.pastTableView)}
                        </Link>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </Fragment>
            ))
          )}
        </TableBody>
      </Table>

      {hasNextPage ? (
        <Button
          onClick={() => fetchNextPage()}
          loading={isFetchingNextPage}
          variant="grey"
          className="mt-6"
        >
          {t(LOCIZE_AGREEMENTS_KEYS.loadMore)}
        </Button>
      ) : null}

      <SmallDialog
        open={!!pastAgreementReferenceKey}
        onOpenChange={onModalOpenChange}
      >
        <Suspense>
          {pastAgreementReferenceKey ? (
            <PastAgreementDialog
              agreementReferenceKey={pastAgreementReferenceKey}
            />
          ) : null}
        </Suspense>
      </SmallDialog>
    </div>
  );
};

function PastAgreementsTableBodySkeleton() {
  return Array.from({ length: 3 }).map((_, rowIndex) => (
    <TableRow key={`past-agreement-row-${rowIndex.toString()}`}>
      {Array.from({ length: 2 }).map((_, cellIndex) => (
        <TableCell
          key={`past-agreement-cell-${(cellIndex + rowIndex).toString()}`}
        >
          <Skeleton className="h-[1.25rem] w-28" />
        </TableCell>
      ))}
      <TableCell key={`past-agreement-cell-button-${rowIndex.toString()}`}>
        <Skeleton className="size-5" />
      </TableCell>
    </TableRow>
  ));
}
