import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import {
  SmallDialog,
  SmallDialogContent,
  SmallDialogDescription,
  SmallDialogFooter,
  SmallDialogHeader,
  SmallDialogTitle,
} from '@components/ui/small-dialog';
import { APP_CONFIG } from '@config/app';
import {
  LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { PURCHASE_FLOW_ROUTE_NAME, ROUTE_NAMES } from '@config/routes';
import { MarketingSearchParamValue } from '@config/search-params';
import { useGetAnalyticsCampaignParametersFromSearch } from '@entities/analytics';
import { useUserCreditAccount } from '@entities/user';
import { useAppConfig, useIsMobileView } from '@hooks/system';
import { Link } from '@tanstack/react-router';
import { addMarketingParamsToUrl } from '@utils/url';
import c from 'clsx';
import { type FC, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { CreditAccountStatus } from '@/shared/types';

type CreditLineBalanceNoSignedInterestFreeProps = {
  isCreditLinePage: boolean;
};

export const CreditLineBalanceNoSignedInterestFree: FC<
  CreditLineBalanceNoSignedInterestFreeProps
> = ({ isCreditLinePage }) => {
  const { t, i18n } = useTranslation(LOCIZE_NAMESPACES.creditLineBalanceWidget);
  const isMobileView = useIsMobileView();
  const { supportUrl } = useAppConfig();
  const { data: creditAccount } = useUserCreditAccount();
  const [isOpenModal, setIsOpenModal] = useState(false);
  const currentUtmParams = useGetAnalyticsCampaignParametersFromSearch();

  const handleCtaButtonClick = () => {
    if (
      creditAccount?.status === CreditAccountStatus.TERMINATED ||
      creditAccount?.status === CreditAccountStatus.DELETED
    ) {
      setIsOpenModal(true);
      return;
    }

    window.open(
      addMarketingParamsToUrl({
        url: PURCHASE_FLOW_ROUTE_NAME.creditLineInterestFree,
        currentUtmParams,
        fallbackUtmSource:
          MarketingSearchParamValue.UTM_SOURCE_CUSTOMER_PROFILE,
      }),
      '_self',
    );
  };

  return (
    <div className="relative">
      <div className="bg-transparent md:px-10 size-full rounded-3xl grid gap-10 min-[875px]:grid-cols-[1fr_0.58fr] md:bg-neutral-50">
        <div
          className={c(
            'py-0 flex flex-col items-center md:block md:py-[2.78125rem]',
            isCreditLinePage && 'md:py-[2.3125rem]',
          )}
        >
          <Typography className="text-neutral-500" variant="text-s">
            {t(
              LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedSecondaryDisclaimer,
            )}
          </Typography>
          <div className="relative bg-white block max-w-[14.125rem] md:hidden">
            <img
              src={`/images/dashboard/withdraw-banner/${i18n.language}.webp`}
              alt="withdraw-banner"
            />
            <div
              className="absolute top-0 left-0 w-full h-full bg-gradient-to-b mix-blend-color-burn from-[#d3d3d3] via-[#d3d3d3] via-[percentage:44.64%] to-primary-white"
              style={{ transform: 'translate3d(0, 0, 0)' }}
            />
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent via-transparent via-[percentage:44.64%] to-primary-white" />
          </div>
          <Typography
            className="mt-4 md:mt-6"
            variant={isMobileView ? 'l' : 'xl'}
          >
            {t(
              LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedSecondaryPromotion1,
            )}
          </Typography>
          <Typography className="mt-2" variant={isMobileView ? 'xxs' : 's'}>
            {t(
              LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedSecondaryPromotion2,
              {
                interestFreeDays:
                  APP_CONFIG.creditLine.creditLineInterestFreeDays,
              },
            )}
          </Typography>
          <div className="w-full mt-8 flex justify-center md:justify-start gap-4">
            <Button
              className={c(isCreditLinePage && 'w-full md:w-auto')}
              size={isCreditLinePage ? 'regular' : 'small'}
              onClick={handleCtaButtonClick}
            >
              {t(
                LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedSecondaryApplyButton,
              )}
            </Button>
            {!isCreditLinePage ? (
              <Button asChild size="small" variant="grey">
                <Link to={ROUTE_NAMES.creditLine}>
                  {t(
                    LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedSecondaryMoreButton,
                  )}
                </Link>
              </Button>
            ) : null}
          </div>
        </div>
        <div className="relative hidden max-w-[56rem] min-[875px]:block">
          <div className="absolute right-0 bottom-0">
            <img
              className="max-w-[17.1875rem] w-full"
              src={`/images/dashboard/withdraw-banner/${i18n.language}.webp`}
              alt="withdraw-banner"
            />
          </div>
        </div>
      </div>

      <SmallDialog open={isOpenModal} onOpenChange={setIsOpenModal}>
        <SmallDialogContent>
          {creditAccount?.status === CreditAccountStatus.DELETED ? (
            <>
              <SmallDialogHeader>
                <SmallDialogTitle>
                  {t(
                    LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedDeletedCaTitle,
                  )}
                </SmallDialogTitle>
                <SmallDialogDescription>
                  {t(
                    LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedTerminatedCaDescription,
                  )}
                </SmallDialogDescription>
              </SmallDialogHeader>
              <SmallDialogFooter>
                <Button asChild>
                  <a href={supportUrl} target="_blank" rel="noreferrer">
                    {t(
                      LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedDeletedCaSupportButton,
                    )}
                  </a>
                </Button>
              </SmallDialogFooter>
            </>
          ) : null}
          {creditAccount?.status === CreditAccountStatus.TERMINATED ? (
            <SmallDialogHeader>
              <SmallDialogTitle>
                {t(
                  LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedTerminatedCaTitle,
                )}
              </SmallDialogTitle>
              <SmallDialogDescription>
                {t(
                  LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedTerminatedCaDescription,
                )}
              </SmallDialogDescription>
            </SmallDialogHeader>
          ) : null}
        </SmallDialogContent>
      </SmallDialog>
    </div>
  );
};
