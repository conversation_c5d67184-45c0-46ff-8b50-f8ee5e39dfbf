import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { Slider } from '@components/ui/slider';
import {
  SmallDialog,
  SmallDialogContent,
  SmallDialogDescription,
  SmallDialogFooter,
  SmallDialogHeader,
  SmallDialogTitle,
} from '@components/ui/small-dialog';
import {
  LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { PURCHASE_FLOW_ROUTE_NAME, ROUTE_NAMES } from '@config/routes';
import { MarketingSearchParamValue } from '@config/search-params';
import { useGetAnalyticsCampaignParametersFromSearch } from '@entities/analytics';
import { useCreditLineMonthlyPayment } from '@entities/credit-account-withdrawal';
import { pricingApi } from '@entities/pricing';
import { useUserCreditAccount } from '@entities/user';
import { useAppConfig, useIsDesktopView } from '@hooks/system';
import { Link } from '@tanstack/react-router';
import { formatNumber } from '@utils/formatters';
import { addMarketingParamsToUrl } from '@utils/url';
import {
  CreditLineBalanceState,
  useCreditLineBalanceState,
} from '@widgets/credit-line-balance';
import { type FC, useEffect, useMemo, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import { CreditAccountStatus } from '@/shared/types';

import { PRICING_KEYS } from '../../config';

type CreditLineBalanceNoSignedProps = {
  isCreditLinePage: boolean;
};

export const CreditLineBalanceNoSigned: FC<CreditLineBalanceNoSignedProps> = ({
  isCreditLinePage,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditLineBalanceWidget);

  const { supportUrl } = useAppConfig();
  const [isOpenModal, setIsOpenModal] = useState(false);
  const isDesktopView = useIsDesktopView();
  const { data: creditAccount } = useUserCreditAccount();
  const currentUtmParams = useGetAnalyticsCampaignParametersFromSearch();
  const { data } = pricingApi.useSuspensePricingQuery({
    keys: [
      PRICING_KEYS.creditAccExampleMaxAmount,
      PRICING_KEYS.creditAccExampleMinAmount,
      PRICING_KEYS.creditAccMaxApr,
      PRICING_KEYS.creditAccFractionPrincipal,
    ],
  });
  const creditLineBalanceState = useCreditLineBalanceState();

  const [amount, setAmount] = useState(0);

  const settings = useMemo(
    () =>
      (data?.pricing ?? []).reduce<{
        minAmount: number;
        maxAmount: number;
        maxApr: number;
        fractionPrincipal: number;
      }>(
        (acc, p) => {
          switch (p?.key) {
            case PRICING_KEYS.creditAccExampleMinAmount:
              acc.minAmount = +p.value;
              break;
            case PRICING_KEYS.creditAccExampleMaxAmount:
              acc.maxAmount = +p.value;
              break;
            case PRICING_KEYS.creditAccMaxApr:
              acc.maxApr = +p.value;
              break;
            case PRICING_KEYS.creditAccFractionPrincipal:
              acc.fractionPrincipal = +p.value;
              break;
          }

          return acc;
        },
        {
          minAmount: 0,
          maxAmount: 0,
          maxApr: 0,
          fractionPrincipal: 0,
        },
      ),
    [data?.pricing],
  );

  const monthlyPayment = useCreditLineMonthlyPayment({
    annualPctRate: settings.maxApr,
    maxPeriodMonths: settings.fractionPrincipal,
    unpaidPrincipal: 0,
    newAmount: amount,
  });

  const handleCtaButtonClick = () => {
    if (
      creditAccount?.status === CreditAccountStatus.TERMINATED ||
      creditAccount?.status === CreditAccountStatus.DELETED
    ) {
      setIsOpenModal(true);
      return;
    }

    window.open(
      addMarketingParamsToUrl({
        url: PURCHASE_FLOW_ROUTE_NAME.creditLine,
        currentUtmParams,
        fallbackUtmSource:
          MarketingSearchParamValue.UTM_SOURCE_CUSTOMER_PROFILE,
      }),
      '_self',
    );
  };

  useEffect(() => {
    if (settings.maxAmount) setAmount(+(settings.maxAmount / 2).toFixed(0));
  }, [settings]);

  return (
    <>
      <div className="relative min-h-[26.25rem]">
        <div className="bg-neutral-50 size-full rounded-3xl p-2 grid lg:text-left lg:grid-cols-[1fr_minmax(18rem,_1fr)] md:gap-10 md:p-10">
          <div className="flex flex-col items-center py-8 text-center lg:text-left lg:items-start lg:justify-center gap-6 lg:p-0">
            <Typography variant={!isDesktopView ? 'l' : 'xl'}>
              {t(LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedHeading)}
            </Typography>
            {isCreditLinePage ? (
              <Typography
                variant={isDesktopView ? 'xxs' : 'm'}
                className="text-neutral-500"
                affects="semibold"
              >
                <Trans
                  i18nKey={
                    LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedDescription
                  }
                  t={t}
                  components={{
                    highlight: <span className="text-primary-black" />,
                  }}
                />
              </Typography>
            ) : (
              <Button variant="white" className="w-max" asChild>
                <Link to={ROUTE_NAMES.creditLine}>
                  {t(
                    LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedDescriptionLink,
                  )}
                </Link>
              </Button>
            )}
          </div>
          <div className="bg-primary-white rounded-3xl grid gap-6 p-6 md:p-10">
            <div className="grid gap-6">
              <div className="grid grid-cols-[1fr_auto] gap-6 items-center">
                <Typography>
                  {t(LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedFormAmount)}
                </Typography>
                <Typography variant="xxs">
                  {amount.toLocaleString()} €
                </Typography>
              </div>
              <Slider
                max={settings.maxAmount}
                min={settings.minAmount}
                onValueChange={(value) => setAmount(value[0])}
                step={10}
                size={isDesktopView ? 'small' : 'regular'}
                value={[amount]}
              />
              <div className="grid grid-cols-[1fr_auto] gap-6 items-center">
                <Typography>
                  {t(
                    LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedFormMonthlyPayment,
                  )}
                </Typography>
                <Typography>
                  {formatNumber({ value: monthlyPayment })} €
                </Typography>
              </div>
            </div>
            <div className="flex flex-col gap-6 pt-4 text-center">
              <Button onClick={handleCtaButtonClick}>
                {t(LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedFormButton)}
              </Button>
              <Typography variant="text-s" className="text-neutral-400">
                {t(LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedFormCaption)}
              </Typography>
            </div>
          </div>
        </div>
        {creditLineBalanceState === CreditLineBalanceState.NO_SIGNED ? (
          <div className="h-20 absolute -bottom-2 left-0 w-full bg-system-green rounded-3xl -z-10" />
        ) : null}
      </div>
      <SmallDialog open={isOpenModal} onOpenChange={setIsOpenModal}>
        <SmallDialogContent>
          {creditAccount?.status === CreditAccountStatus.DELETED ? (
            <>
              <SmallDialogHeader>
                <SmallDialogTitle>
                  {t(
                    LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedDeletedCaTitle,
                  )}
                </SmallDialogTitle>
                <SmallDialogDescription>
                  {t(
                    LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedTerminatedCaDescription,
                  )}
                </SmallDialogDescription>
              </SmallDialogHeader>
              <SmallDialogFooter>
                <Button asChild>
                  <a href={supportUrl} target="_blank" rel="noreferrer">
                    {t(
                      LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedDeletedCaSupportButton,
                    )}
                  </a>
                </Button>
              </SmallDialogFooter>
            </>
          ) : null}
          {creditAccount?.status === CreditAccountStatus.TERMINATED ? (
            <SmallDialogHeader>
              <SmallDialogTitle>
                {t(
                  LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedTerminatedCaTitle,
                )}
              </SmallDialogTitle>
              <SmallDialogDescription>
                {t(
                  LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS.noSignedTerminatedCaDescription,
                )}
              </SmallDialogDescription>
            </SmallDialogHeader>
          ) : null}
        </SmallDialogContent>
      </SmallDialog>
    </>
  );
};
