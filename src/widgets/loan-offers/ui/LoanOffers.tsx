import { Typography } from '@components/typography';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@components/ui/carousel';
import { APP_CONFIG } from '@config/app';
import { LOCIZE_NAMESPACES, LOCIZE_OFFERS_KEYS } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { OfferCard } from '@entities/product/offer';
import { useAppConfig, useIsMobileView } from '@hooks/system';
import { Link, useNavigate, useSearch } from '@tanstack/react-router';
import { cn } from '@utils/tailwind';
import c from 'clsx';
import { type FC, lazy, Suspense } from 'react';
import { useTranslation } from 'react-i18next';

import { NonLoanProduct } from '@/shared/types';

import { useLoanOffers } from '../hooks';

const LoanOfferModal = lazy(() =>
  import('@widgets/loan-offer-modal').then((module) => ({
    default: module.LoanOfferModal,
  })),
);

type LoanOffersProps = {
  className?: string;
};

export const LoanOffers: FC<LoanOffersProps> = ({ className }) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.offers);
  const { starProduct } = useAppConfig();
  const { offers } = useLoanOffers();
  const { selectedLoanOffer, ...currentSearch } = useSearch({
    strict: false,
  });
  const navigate = useNavigate();
  const isMobileView = useIsMobileView();

  if (!offers?.length) {
    return null;
  }

  return (
    <>
      <div className={c('grid gap-1 pb-7 md:p-0 w-full', className)}>
        <Typography tag="h2" variant="xs" className="px-6 md:px-0">
          {t(LOCIZE_OFFERS_KEYS.blockTitle)}
        </Typography>
        <Carousel
          className=" grid gap-1 pb-7 md:p-0 w-full"
          opts={{ align: 'start', dragFree: true }}
        >
          <CarouselContent isInteractive className="-ml-0 mr-5 py-5 md:-ml-6">
            {offers.map((offer) => {
              const creditLineCardWidth = isMobileView
                ? 'basis-[16.625rem]'
                : 'basis-[26.25rem]';
              return (
                <CarouselItem
                  key={`offer: ${offer.productType}`}
                  className={cn(
                    'pl-6',
                    offer.productType === NonLoanProduct.CREDIT_LINE
                      ? creditLineCardWidth
                      : 'basis-[16.625rem]',
                  )}
                >
                  <Link
                    to={ROUTE_NAMES.current}
                    search={{
                      ...currentSearch,
                      selectedLoanOffer: offer.productType,
                    }}
                    resetScroll={false}
                  >
                    <OfferCard
                      isStarProduct={
                        offer.productType === APP_CONFIG.starProduct
                      }
                      className={cn('transition-shadow')}
                      {...offer}
                    />
                  </Link>
                </CarouselItem>
              );
            })}
          </CarouselContent>
        </Carousel>
      </div>

      <Suspense>
        {selectedLoanOffer && (
          <LoanOfferModal
            open={!!selectedLoanOffer}
            onOpenChange={(state: boolean) => {
              if (!state) {
                navigate({
                  to: ROUTE_NAMES.current,
                  replace: true,
                });
              }
            }}
            productType={selectedLoanOffer}
            isStarProduct={selectedLoanOffer === starProduct}
          />
        )}
      </Suspense>
    </>
  );
};
