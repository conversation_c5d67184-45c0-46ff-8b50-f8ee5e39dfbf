import { LOCIZE_NAMESPACES, LOCIZE_OFFERS_KEYS } from '@config/locize';
import type { OfferCardProps } from '@entities/product/offer';
import { useUserCreditAccount } from '@entities/user';
import { serializeCreditLineSettings } from '@utils/serializeCreditLineSettings';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { ConsumerLoanProduct, NonLoanProduct } from '@/shared/types';

import { loanOffersApi } from './api';

export type DashboardOfferItem = Pick<
  OfferCardProps,
  'title' | 'description' | 'img' | 'productType'
>;

const useLoanOffers = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.offers);
  const { data } = loanOffersApi.useSuspenseLoanOffersSettingsQuery();
  const { data: creditAccount } = useUserCreditAccount();

  const offers = useMemo(() => {
    if (!data) {
      return null;
    }

    const offersList: Array<DashboardOfferItem> = [];

    if (data.CREDIT_LINE && !creditAccount?.isActive) {
      const { maxAmount } = serializeCreditLineSettings(data.CREDIT_LINE);

      offersList.push({
        title: t(LOCIZE_OFFERS_KEYS.creditLineTitle),
        img: '/images/dashboard/products/credit-line-v2.webp',
        productType: NonLoanProduct.CREDIT_LINE,
        description: [
          t(LOCIZE_OFFERS_KEYS.creditLineOfferDescription1, {
            amount: maxAmount,
          }),
          t(LOCIZE_OFFERS_KEYS.creditLineOfferDescription2),
        ],
      });
    }

    if (data.APPLICATION?.SMALL_LOAN) {
      const { max_loan_amount, possible_periods } = data.APPLICATION.SMALL_LOAN;

      offersList.push({
        title: t(LOCIZE_OFFERS_KEYS.smallLoanTitle),
        img: '/images/dashboard/products/small-loan-v2.webp',
        productType: ConsumerLoanProduct.SMALL_LOAN,
        description: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: max_loan_amount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possible_periods.at(-1) ?? 0,
          }),
        ],
      });
    }

    if (data.APPLICATION?.FAST_LOAN) {
      const { max_loan_amount, possible_periods } = data.APPLICATION.FAST_LOAN;

      offersList.push({
        title: t(LOCIZE_OFFERS_KEYS.fastLoanTitle),
        img: '/images/dashboard/products/fast-loan-v2.webp',
        productType: ConsumerLoanProduct.FAST_LOAN,
        description: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: max_loan_amount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possible_periods.at(-1) ?? 0,
          }),
        ],
      });
    }

    if (data.APPLICATION?.RENOVATION_LOAN) {
      const { max_loan_amount, possible_periods } =
        data.APPLICATION.RENOVATION_LOAN;

      offersList.push({
        title: t(LOCIZE_OFFERS_KEYS.renovationLoanTitle),
        img: '/images/dashboard/products/renovation-loan-v2.webp',
        productType: ConsumerLoanProduct.RENOVATION_LOAN,
        description: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: max_loan_amount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possible_periods.at(-1) ?? 0,
          }),
        ],
      });
    }

    if (data.APPLICATION?.VEHICLE_LOAN) {
      const { max_loan_amount, possible_periods } =
        data.APPLICATION.VEHICLE_LOAN;

      offersList.push({
        title: t(LOCIZE_OFFERS_KEYS.vehicleLoanTitle),
        img: '/images/dashboard/products/vehicle-loan-v2.webp',
        productType: ConsumerLoanProduct.VEHICLE_LOAN,
        description: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: max_loan_amount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possible_periods.at(-1) ?? 0,
          }),
        ],
      });
    }

    if (data.APPLICATION?.TRAVEL_LOAN) {
      const { max_loan_amount, possible_periods } =
        data.APPLICATION.TRAVEL_LOAN;

      offersList.push({
        title: t(LOCIZE_OFFERS_KEYS.travelLoanTitle),
        img: '/images/dashboard/products/travel-loan.webp',
        productType: ConsumerLoanProduct.TRAVEL_LOAN,
        description: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: max_loan_amount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possible_periods.at(-1) ?? 0,
          }),
        ],
      });
    }

    if (data.APPLICATION?.HEALTH_LOAN) {
      const { max_loan_amount, possible_periods } =
        data.APPLICATION.HEALTH_LOAN;

      offersList.push({
        title: t(LOCIZE_OFFERS_KEYS.healthLoanTitle),
        img: '/images/dashboard/products/health-loan.webp',
        productType: ConsumerLoanProduct.HEALTH_LOAN,
        description: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: max_loan_amount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possible_periods.at(-1) ?? 0,
          }),
        ],
      });
    }

    if (data.APPLICATION?.BEAUTY_LOAN) {
      const { max_loan_amount, possible_periods } =
        data.APPLICATION.BEAUTY_LOAN;

      offersList.push({
        title: t(LOCIZE_OFFERS_KEYS.beautyLoanTitle),
        img: '/images/dashboard/products/beauty-loan.webp',
        productType: ConsumerLoanProduct.BEAUTY_LOAN,
        description: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: max_loan_amount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possible_periods.at(-1) ?? 0,
          }),
        ],
      });
    }
    return offersList;
  }, [data, t, creditAccount]);

  return { offers };
};

export { useLoanOffers };
