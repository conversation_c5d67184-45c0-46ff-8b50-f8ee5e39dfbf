/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../shared/types/api.gen';

import {
  useQuery,
  useSuspenseQuery,
  UseQueryOptions,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';
import { fetcher } from '@lib/fetcher';
export type LoanSettingsFragment = {
  max_loan_amount: number;
  min_loan_amount: number;
  default_period?: number | null;
  default_loan_amount?: number | null;
  possible_periods: Array<number | null>;
};

export type LoanOffersSettingsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type LoanOffersSettingsQuery = {
  APPLICATION?: {
    SMALL_LOAN: {
      max_loan_amount: number;
      min_loan_amount: number;
      default_period?: number | null;
      default_loan_amount?: number | null;
      possible_periods: Array<number | null>;
    };
    FAST_LOAN: {
      max_loan_amount: number;
      min_loan_amount: number;
      default_period?: number | null;
      default_loan_amount?: number | null;
      possible_periods: Array<number | null>;
    };
    RENOVATION_LOAN: {
      max_loan_amount: number;
      min_loan_amount: number;
      default_period?: number | null;
      default_loan_amount?: number | null;
      possible_periods: Array<number | null>;
    };
    VEHICLE_LOAN: {
      max_loan_amount: number;
      min_loan_amount: number;
      default_period?: number | null;
      default_loan_amount?: number | null;
      possible_periods: Array<number | null>;
    };
    TRAVEL_LOAN: {
      max_loan_amount: number;
      min_loan_amount: number;
      default_period?: number | null;
      default_loan_amount?: number | null;
      possible_periods: Array<number | null>;
    };
    HEALTH_LOAN: {
      max_loan_amount: number;
      min_loan_amount: number;
      default_period?: number | null;
      default_loan_amount?: number | null;
      possible_periods: Array<number | null>;
    };
    BEAUTY_LOAN: {
      max_loan_amount: number;
      min_loan_amount: number;
      default_period?: number | null;
      default_loan_amount?: number | null;
      possible_periods: Array<number | null>;
    };
  } | null;
  CREDIT_LINE?: Array<{ key: string; value: string } | null> | null;
};

export const LoanSettingsFragmentDoc = `
    fragment LoanSettings on ScheduleTypeSettings {
  max_loan_amount
  min_loan_amount
  default_period
  default_loan_amount
  possible_periods
}
    `;
export const LoanOffersSettingsDocument = `
    query LoanOffersSettings {
  APPLICATION: schedule_types_settings {
    SMALL_LOAN {
      ...LoanSettings
    }
    FAST_LOAN {
      ...LoanSettings
    }
    RENOVATION_LOAN {
      ...LoanSettings
    }
    VEHICLE_LOAN {
      ...LoanSettings
    }
    TRAVEL_LOAN {
      ...LoanSettings
    }
    HEALTH_LOAN {
      ...LoanSettings
    }
    BEAUTY_LOAN {
      ...LoanSettings
    }
  }
  CREDIT_LINE: pricing(
    keys: ["credit_acc.example_max_amount", "credit_acc.example_min_amount"]
  ) {
    key
    value
  }
}
    ${LoanSettingsFragmentDoc}`;

export const useLoanOffersSettingsQuery = <
  TData = LoanOffersSettingsQuery,
  TError = unknown,
>(
  variables?: LoanOffersSettingsQueryVariables,
  options?: Omit<
    UseQueryOptions<LoanOffersSettingsQuery, TError, TData>,
    'queryKey'
  > & {
    queryKey?: UseQueryOptions<
      LoanOffersSettingsQuery,
      TError,
      TData
    >['queryKey'];
  },
) => {
  return useQuery<LoanOffersSettingsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ['LoanOffersSettings']
        : ['LoanOffersSettings', variables],
    queryFn: fetcher<LoanOffersSettingsQuery, LoanOffersSettingsQueryVariables>(
      LoanOffersSettingsDocument,
      variables,
    ),
    ...options,
  });
};

useLoanOffersSettingsQuery.getKey = (
  variables?: LoanOffersSettingsQueryVariables,
) =>
  variables === undefined
    ? ['LoanOffersSettings']
    : ['LoanOffersSettings', variables];

export const useSuspenseLoanOffersSettingsQuery = <
  TData = LoanOffersSettingsQuery,
  TError = unknown,
>(
  variables?: LoanOffersSettingsQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<LoanOffersSettingsQuery, TError, TData>,
    'queryKey'
  > & {
    queryKey?: UseSuspenseQueryOptions<
      LoanOffersSettingsQuery,
      TError,
      TData
    >['queryKey'];
  },
) => {
  return useSuspenseQuery<LoanOffersSettingsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ['LoanOffersSettingsSuspense']
        : ['LoanOffersSettingsSuspense', variables],
    queryFn: fetcher<LoanOffersSettingsQuery, LoanOffersSettingsQueryVariables>(
      LoanOffersSettingsDocument,
      variables,
    ),
    ...options,
  });
};

useSuspenseLoanOffersSettingsQuery.getKey = (
  variables?: LoanOffersSettingsQueryVariables,
) =>
  variables === undefined
    ? ['LoanOffersSettingsSuspense']
    : ['LoanOffersSettingsSuspense', variables];

useLoanOffersSettingsQuery.fetcher = (
  variables?: LoanOffersSettingsQueryVariables,
  options?: RequestInit['headers'],
) =>
  fetcher<LoanOffersSettingsQuery, LoanOffersSettingsQueryVariables>(
    LoanOffersSettingsDocument,
    variables,
    options,
  );
