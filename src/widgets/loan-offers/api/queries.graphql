fragment LoanSettings on ScheduleTypeSettings {
  max_loan_amount
  min_loan_amount
  default_period
  default_loan_amount
  possible_periods
}

query LoanOffersSettings {
  APPLICATION: schedule_types_settings {
    SMALL_LOAN {
      ...LoanSettings
    }
    FAST_LOAN {
      ...LoanSettings
    }
    RENOVATION_LOAN {
      ...LoanSettings
    }
    VEHICLE_LOAN {
      ...LoanSettings
    }
    TRAVEL_LOAN {
      ...LoanSettings
    }
    HEALTH_LOAN {
      ...LoanSettings
    }
    BEAUTY_LOAN {
      ...LoanSettings
    }
  }
  CREDIT_LINE: pricing(
    keys: ["credit_acc.example_max_amount", "credit_acc.example_min_amount"]
  ) {
    key
    value
  }
}
