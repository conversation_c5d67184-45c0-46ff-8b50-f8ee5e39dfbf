import { AppDialog as Dialog } from '@components/AppDialog';
import { Image } from '@components/image';
import { Typography } from '@components/typography';
import {
  LOCIZE_COMMON_KEYS,
  LOCIZE_NAMESPACES,
  LOCIZE_OFFERS_KEYS,
} from '@config/locize';
import { ConsumerLoanApplyButton } from '@features/consumer-loan/create';
import { CreditLineApplyButton } from '@features/credit-line/create';
import CheckBoldIcon from '@icons/check-bold.svg?react';
import { X } from 'lucide-react';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import {
  ConsumerLoanProduct,
  type EstoProductType,
  NonLoanProduct,
} from '@/shared/types';

import { useLoanOfferModalConfig } from '../hooks';

type LoanOfferModalProps = {
  productType: EstoProductType;
  isStarProduct?: boolean;
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export const LoanOfferModal: FC<LoanOfferModalProps> = ({
  productType,
  open,
  onOpenChange,
  isStarProduct = false,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.offers);
  const { t: tc } = useTranslation(LOCIZE_NAMESPACES.common);
  const modalConfig = useLoanOfferModalConfig(productType);

  const renderActionButton = () => {
    switch (productType) {
      case NonLoanProduct.CREDIT_LINE:
        return (
          <CreditLineApplyButton fullWidth>
            {tc(LOCIZE_COMMON_KEYS.applyButton)}
          </CreditLineApplyButton>
        );
      case ConsumerLoanProduct.FAST_LOAN:
      case ConsumerLoanProduct.RENOVATION_LOAN:
      case ConsumerLoanProduct.VEHICLE_LOAN:
      case ConsumerLoanProduct.SMALL_LOAN:
      case ConsumerLoanProduct.TRAVEL_LOAN:
      case ConsumerLoanProduct.HEALTH_LOAN:
      case ConsumerLoanProduct.BEAUTY_LOAN:
        return (
          <ConsumerLoanApplyButton productType={productType} fullWidth>
            {tc(LOCIZE_COMMON_KEYS.applyButton)}
          </ConsumerLoanApplyButton>
        );
    }
  };

  return (
    <Dialog
      className="md:max-w-[31.27rem]"
      title={t(modalConfig.title)}
      open={open}
      onOpenChange={onOpenChange}
      customCloseButton={<X className="size-8" />}
    >
      <div className="flex flex-col">
        <div className="relative overflow-hidden">
          {isStarProduct && (
            <div
              className={
                'absolute top-0 right-0 flex h-7 w-40 z-10 translate-x-11 translate-y-6 rotate-45 items-center justify-center bg-system-green2'
              }
            >
              <Typography
                variant="text-s"
                tag="span"
                className="text-primary-white"
              >
                {t(LOCIZE_OFFERS_KEYS.starProductLabel)}
              </Typography>
            </div>
          )}
          <Image
            src={modalConfig.imgSrc}
            alt={t(LOCIZE_OFFERS_KEYS.creditLineTitle)}
            className="w-full md:max-w-[27rem] md:max-h-[18.7rem] object-cover rounded-xl aspect-square"
          />
        </div>

        <div className="flex flex-col gap-2 pt-7">
          {modalConfig.features.map((step, index) => (
            <li key={index} className="flex items-center gap-2 pt-1">
              <CheckBoldIcon className={'text-primary-green'} />
              <Typography variant="text-m" affects="semibold">
                {step}
              </Typography>
            </li>
          ))}
          <Typography className="pt-4">{t(modalConfig.description)}</Typography>

          <div className="mt-10">{renderActionButton()}</div>
        </div>
      </div>
    </Dialog>
  );
};
