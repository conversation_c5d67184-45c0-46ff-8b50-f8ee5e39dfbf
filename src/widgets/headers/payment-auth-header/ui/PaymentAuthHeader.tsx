import { Button } from '@components/ui/button';
import { APP_CONFIG } from '@config/app';
import { LOCIZE_AUTH_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { LanguageSelector } from '@features/language-selector';
import { useNavigate } from '@tanstack/react-router';
import { useTranslation } from 'react-i18next';

export const PaymentAuthHeader = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.auth);
  const navigate = useNavigate();

  const onCtaClick = () => {
    navigate({
      to: ROUTE_NAMES.auth,
      search: {
        authMethod: APP_CONFIG.authMethods[0],
      },
    });
  };

  return (
    <div className="flex flex-row items-end items-center justify-end gap-6">
      <LanguageSelector />
      <Button onClick={onCtaClick}>{t(LOCIZE_AUTH_KEYS.logIn)}</Button>
    </div>
  );
};
