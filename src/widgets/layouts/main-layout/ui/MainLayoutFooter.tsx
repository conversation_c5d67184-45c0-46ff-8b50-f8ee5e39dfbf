import { Typography } from '@components/typography';
import { APP_CONFIG } from '@config/app';
import { LOCIZE_COMMON_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { Trans, useTranslation } from 'react-i18next';

export const MainLayoutFooter = () => {
  const { t, i18n } = useTranslation(LOCIZE_NAMESPACES.common);

  return (
    <footer className="p-6">
      <Typography
        className="hidden text-center text-neutral-400 md:block"
        variant="text-s"
      >
        <Trans
          components={{
            termsLink: (
              <a
                key="terms-link"
                aria-label="Go to terms and conditions page"
                className="underline hover:opacity-70"
                href={APP_CONFIG.termsUrlByLanguage[i18n.language]}
              />
            ),
          }}
          i18nKey={LOCIZE_COMMON_KEYS.footerText}
          t={t}
          values={{
            name: APP_CONFIG.legalInfo.name,
            address: APP_CONFIG.legalInfo.address,
          }}
        />
      </Typography>
      <div className="grid gap-2 md:hidden">
        <Typography variant="text-s">© {APP_CONFIG.legalInfo.name}</Typography>
        <div className="grid grid-cols-2 items-center gap-2">
          <a href="mailto:<EMAIL>">
            <Typography variant="text-s">
              {APP_CONFIG.legalInfo.infoEmail}
            </Typography>
          </a>
          <a href={`tel:${APP_CONFIG.legalInfo.phone.number}`}>
            <Typography variant="text-s">
              {APP_CONFIG.legalInfo.phone.label}
            </Typography>
          </a>
        </div>
      </div>
    </footer>
  );
};
