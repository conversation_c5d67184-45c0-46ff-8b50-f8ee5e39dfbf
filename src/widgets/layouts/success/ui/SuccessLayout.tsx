import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { LOCIZE_COMMON_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import CheckRoundedIcon from '@icons/check-rounded.svg?react';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

type SuccessLayoutProps = {
  title: string;
  description: string;
  onClose: () => void;
  before?: React.ReactNode;
};

export const SuccessLayout: FC<SuccessLayoutProps> = ({
  title,
  description,
  onClose,
  before,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.common);

  return (
    <main className="size-full px-8 pb-8 pt-[65px]">
      <div className="grid max-w-[400px] mx-auto py-12">
        <div className="mx-auto">
          <CheckRoundedIcon />
        </div>
        <div className="grid gap-8 text-center mt-6 mb-14">
          <Typography variant="m">{title}</Typography>
          <Typography affects="semibold" variant="text-l">
            {description}
          </Typography>
          {before}
        </div>
        <Button onClick={onClose} className="mt-8">
          {t(LOCIZE_COMMON_KEYS.close)}
        </Button>
      </div>
    </main>
  );
};
