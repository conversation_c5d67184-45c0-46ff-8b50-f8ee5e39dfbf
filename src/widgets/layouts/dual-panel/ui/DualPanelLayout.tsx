import type { ROUTE_NAMES } from '@config/routes';
import CloseIcon from '@icons/close-sharp.svg?react';
import { Link } from '@tanstack/react-router';
import type { FC, PropsWithChildren, ReactNode } from 'react';

type DualPanelLayoutProps = PropsWithChildren<{
  header?: ReactNode;
  left: ReactNode;
  right: ReactNode;
  fromPage: (typeof ROUTE_NAMES)[keyof typeof ROUTE_NAMES];
}>;

/**
 * DualPanelLayout component provides a responsive dual-panel layout with a left and a right panel.
 *
 * @param {(typeof ROUTE_NAMES)[keyof typeof ROUTE_NAMES]} fromPage - The route name to navigate back to, typically used for the close button functionality.
 * @param {ReactNode} left - The content to be displayed in the left panel.
 * @param {ReactNode} right - The content to be displayed in the right panel.
 * @returns {JSX.Element} A JSX element representing the dual-panel layout.
 */
export const DualPanelLayout: FC<DualPanelLayoutProps> = ({
  fromPage,
  header = (
    <Link to={fromPage}>
      <CloseIcon />
    </Link>
  ),
  left,
  right,
}) => (
  <div className="bg-primary-white relative flex flex-col p-6 pt-0 h-full md:p-8 md:pt-0">
    <header className="bg-primary-white/80 sticky top-0 left-0 flex justify-end pt-6 md:pt-8">
      <div className="transition-colors hover:text-neutral-700">{header}</div>
    </header>
    <main className="grid grid-cols-1 pt-12 pb-20 md:pb-[70px] md:m-auto md:max-w-[1376px] md:max-h-[980px] md:size-full md:grid-cols-2 md:grid-rows-1 md:grid-areas-[left_right]">
      {left ? (
        <div className="rounded-3xl w-full flex items-center justify-center md:bg-[#ECF2F4] md:grid-in-[left] md:p-6">
          {left}
        </div>
      ) : null}
      {right ? (
        <div className="w-full flex items-center justify-center md:grid-in-[right] md:p-6">
          {right}
        </div>
      ) : null}
    </main>
  </div>
);
