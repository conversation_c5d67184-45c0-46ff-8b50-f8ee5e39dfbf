import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { Skeleton } from '@components/ui/skeleton';
import c from 'clsx';
import type { FC } from 'react';

type ManualTransferInfoProps = {
  onCancel?: () => void;
  cancelButtonLabel: string;
  transferInfo: Nullish<Array<{ label: string; value: string }>>;
  title?: string;
  description?: string;
};

export const ManualTransferInfo: FC<ManualTransferInfoProps> = ({
  onCancel,
  cancelButtonLabel,
  transferInfo,
  title,
  description,
}) => (
  <div className="grid gap-14">
    {!transferInfo ? (
      <>
        <Skeleton className="rounded-2xl h-[361px]" />
      </>
    ) : (
      <div className="grid rounded-2xl border border-neutral-200 overflow-hidden">
        {title || description ? (
          <div
            className={c(
              'grid gap-2.5 p-4 text-center bg-neutral-50',
              !title || (!description && 'gap-2.5'),
            )}
          >
            <Typography affects="semibold">{title}</Typography>
            <Typography variant="text-s">{description}</Typography>
          </div>
        ) : null}
        <div className="grid divide-y-[1px] divide-neutral-200 space-y-3 p-4">
          {transferInfo.map(({ label, value }, index) => (
            <div
              key={label}
              className={c(
                'pt-3 flex items-center justify-between gap-2.5',
                index === 0 && 'pt-0',
                '[&>span:first-child]:text-neutral-500',
              )}
            >
              <Typography variant="text-s" tag="span">
                {label}
              </Typography>
              <Typography variant="text-s" tag="span">
                {value}
              </Typography>
            </div>
          ))}
        </div>
      </div>
    )}
    {onCancel ? <Button onClick={onCancel}>{cancelButtonLabel}</Button> : null}
  </div>
);
