import { ProductIcon } from '@components/ProductIcon';
import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { Skeleton } from '@components/ui/skeleton';
import {
  SmallDialogContent,
  SmallDialogFooter,
  SmallDialogHeader,
} from '@components/ui/small-dialog';
import { LOCIZE_AGREEMENTS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { OLD_APP_ROUTE_NAME, ROUTE_NAMES } from '@config/routes';
import { AGREEMENT_CONFIG_BY_SCHEDULE_TYPE } from '@entities/agreements/config';
import { applicationApi } from '@entities/application/api';
import { AppPaymentMethod } from '@entities/payments';
import { useFeatureToggles, useToast } from '@hooks/system';
import ChevronRightIcon from '@icons/chevron-right.svg?react';
import DownloadIcon from '@icons/download.svg?react';
import { AgreementPaymentFormView } from '@pages/agreement-payment/config';
import { getContractDownloadLink } from '@pages/agreements/utils';
import { getRouteApi, Navigate } from '@tanstack/react-router';
import { formatDate, formatNumber } from '@utils/formatters';
import { getProductByScheduleType } from '@utils/getProductByApplicationScheduleType';
import { cn } from '@utils/tailwind';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useUpdateEffect } from 'react-use';

import { ApplicationScheduleType } from '@/shared/types';

type ActiveAgreementDialogProps = {
  agreementReferenceKey: string;
};

const routerApi = getRouteApi('/_protected/_main');

export const ActiveAgreementDialog: FC<ActiveAgreementDialogProps> = ({
  agreementReferenceKey,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.agreements);

  const navigate = routerApi.useNavigate();

  const { creditAccountConversionFeature } = useFeatureToggles();

  const { data, isSuccess, isError, isFetching } =
    applicationApi.useApplicationQuery(
      {
        referenceKey: agreementReferenceKey,
      },
      {
        select: (data) => {
          if (!data?.application_by_reference) return null;

          const scheduleType = data.application_by_reference.schedule_type;

          const { title, description } =
            AGREEMENT_CONFIG_BY_SCHEDULE_TYPE[scheduleType];

          const agreementInfo = [
            {
              id: 1,
              label: t(LOCIZE_AGREEMENTS_KEYS.modalTableTotal),
              value: `${formatNumber({ value: data.application_by_reference.requested_amount, minimumFractionDigits: 2 })} €`,
            },
            {
              id: 2,
              label: t(LOCIZE_AGREEMENTS_KEYS.modalTablePaid),
              value: `${formatNumber({ value: data.application_by_reference.paid_principal, minimumFractionDigits: 2 })} €`,
            },
            {
              id: 3,
              label: t(LOCIZE_AGREEMENTS_KEYS.modalTableMonthlyPayment),
              value: `${formatNumber({ value: data.application_by_reference.credit_info?.monthly_payment, minimumFractionDigits: 2 })} €`,
            },
            {
              id: 4,
              label: t(LOCIZE_AGREEMENTS_KEYS.modalTableSigned),
              value: `${formatDate(Number(data.application_by_reference.signed_at) * 1000)}`,
            },
            {
              id: 5,
              label: t(LOCIZE_AGREEMENTS_KEYS.modalTableContracts),
              value: (
                <>
                  <a
                    href={getContractDownloadLink(
                      data.application_by_reference.id,
                      data.application_by_reference.schedule_type,
                    )}
                    target="_blank"
                    rel="noreferrer"
                    className="flex items-center gap-2 rounded-lg bg-neutral-100 px-1 py-[0.125rem] text-primary-black"
                  >
                    {data.application_by_reference.invoice_reference_nr}
                    <DownloadIcon className="w-[0.875rem] text-primary-black" />
                  </a>
                </>
              ),
            },
          ];

          return {
            title,
            description,
            scheduleType,
            isFreeHpEnabled: data.application_by_reference.free_hp_enabled,
            agreementDescription:
              data.application_by_reference.merchant?.name ??
              (description && t(description)),
            canConvertToCreditAccount:
              data.application_by_reference
                .can_manually_convert_to_credit_account,
            agreementInfo,
            productType: getProductByScheduleType(scheduleType),
            id: data.application_by_reference.id,
            schedule_type: data.application_by_reference.schedule_type,
            merchant: {
              campaignName:
                data.application_by_reference.merchant?.campaign
                  ?.converting_schedule_name,
              name: data.application_by_reference.merchant?.name,
              logoSrc: data.application_by_reference.merchant?.logo_path,
            },
          };
        },
      },
    );

  const { showErrorMessage } = useToast();

  const isInvalidApplication = (isSuccess && !data) || isError;

  const onPaymentButtonClick = () => {
    if (data?.isFreeHpEnabled) {
      navigate({
        search: (prev) => ({
          ...prev,
          agreementReferenceKey: undefined,
          agreementPaymentConfirm: undefined,
        }),
      });

      setTimeout(() =>
        navigate({
          to: ROUTE_NAMES.paymentAgreement,
          search: (prev) => ({
            ...prev,
            referenceKey: agreementReferenceKey,
            paymentMethod: AppPaymentMethod.BANKLINK,
            formView: AgreementPaymentFormView.OVERVIEW,
          }),
        }),
      );

      return;
    }

    navigate({
      search: (prev) => ({
        ...prev,
        agreementPaymentConfirm: true,
      }),
    });
  };

  const onReduceMonthlyPaymentClick = () => {
    navigate({
      to: ROUTE_NAMES.creditAccountConversionApply,
      search: {
        referenceKey: agreementReferenceKey,
        fromPathname: location.pathname,
      },
    });
  };

  const onPaymentScheduleClick = () => {
    navigate({
      to: '/agreements/schedule/$applicationId',
      params: {
        applicationId: agreementReferenceKey,
      },
    });
  };

  useUpdateEffect(() => {
    if (isInvalidApplication) {
      showErrorMessage(
        isError ? 'Something went wrong' : 'Agreement not found',
      );
    }
  });

  if ((isSuccess && !data) || isError) {
    return (
      <Navigate
        to="."
        replace
        search={(prev) => ({ ...prev, agreementReferenceKey: undefined })}
      />
    );
  }

  return (
    <SmallDialogContent>
      {isFetching || !data ? (
        <ActiveAgreementDialogSkeleton />
      ) : (
        <>
          <SmallDialogHeader>
            <div className="flex items-center justify-between gap-2">
              <div className="flex flex-col gap-2 items-start">
                <Typography variant="s" affects="semibold">
                  {data.scheduleType === ApplicationScheduleType.ESTO_X
                    ? data.merchant.campaignName
                    : t(data.title)}
                </Typography>
                {data.agreementDescription ? (
                  <Typography variant="text-l" className="text-neutral-500">
                    {data.agreementDescription}
                  </Typography>
                ) : null}
              </div>
              <ProductIcon
                className="ml-4"
                merchantLogoSrc={data.merchant.logoSrc}
                productType={data.productType}
                size="large"
              />
            </div>
          </SmallDialogHeader>
          {data.canConvertToCreditAccount ? (
            creditAccountConversionFeature ? (
              <Button
                asChild
                variant="blue"
                className="mt-2 flex w-full cursor-pointer justify-between rounded-lg"
                onClick={onReduceMonthlyPaymentClick}
              >
                <div>
                  <span className="flex-1">
                    {t(LOCIZE_AGREEMENTS_KEYS.modalReduceMonthlyPayment)}
                  </span>
                  <ChevronRightIcon className="ml-2 text-primary-white" />
                </div>
              </Button>
            ) : (
              <Button
                asChild
                variant="blue"
                className="mt-2 flex w-full justify-between rounded-lg"
              >
                <a
                  href={OLD_APP_ROUTE_NAME.applicationReduceMonthlyPayment.replace(
                    ':applicationReferenceKey',
                    agreementReferenceKey,
                  )}
                >
                  <span className="flex-1">
                    {t(LOCIZE_AGREEMENTS_KEYS.modalReduceMonthlyPayment)}
                  </span>
                  <ChevronRightIcon className="ml-2 text-primary-white" />
                </a>
              </Button>
            )
          ) : null}
          <div
            className={cn(
              'mt-2 divide-y-[1px] px-4',
              data.canConvertToCreditAccount && 'mt-8',
            )}
          >
            {data.agreementInfo.map(({ label, value, id }) => (
              <div
                key={id}
                className="flex items-center justify-between gap-2 py-3"
              >
                <Typography variant="text-s">{label}</Typography>
                <Typography variant="text-s">{value}</Typography>
              </div>
            ))}
          </div>
          <SmallDialogFooter className="mt-4 mb-6 space-y-4">
            <Button
              variant="grey"
              className="w-full"
              onClick={onPaymentScheduleClick}
            >
              {t(LOCIZE_AGREEMENTS_KEYS.modalPaymentSchedule)}
            </Button>
            <Button variant="white" asChild className="w-full">
              <Button onClick={onPaymentButtonClick}>
                {t(LOCIZE_AGREEMENTS_KEYS.modalMakePayment)}
              </Button>
            </Button>
          </SmallDialogFooter>
        </>
      )}
    </SmallDialogContent>
  );
};

function ActiveAgreementDialogSkeleton() {
  return (
    <>
      <SmallDialogHeader>
        <div className="flex w-full items-center justify-between gap-2">
          <div className="flex w-full flex-1 flex-col gap-2">
            <Skeleton className="h-8 w-full bg-gray-300" />
            <Skeleton className="h-6 w-full bg-gray-200" />
          </div>
          <Skeleton className="ml-2 h-[4.5rem] w-[4.5rem] rounded-full bg-gray-300" />
        </div>
      </SmallDialogHeader>

      <div className="mt-2 divide-y-[1px] px-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <Skeleton
            key={`agreement-skeleton-${index.toString()}`}
            className="flex h-11 items-center justify-between gap-2 rounded-none"
          />
        ))}
      </div>
      <SmallDialogFooter className="mt-4 mb-6 space-y-4">
        <Skeleton className="h-12 w-full rounded-full" />
        <Skeleton className="h-12 w-full rounded-full" />
      </SmallDialogFooter>
    </>
  );
}
