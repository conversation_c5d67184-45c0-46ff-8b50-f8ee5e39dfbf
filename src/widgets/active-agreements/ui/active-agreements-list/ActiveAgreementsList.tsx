import { Notification } from '@components/Notification';
import { Typography } from '@components/typography';
import { SmallDialog } from '@components/ui/small-dialog';
import { LOCIZE_AGREEMENTS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { ApplicationAgreement } from '@entities/agreements';
import { useUserCreditAccount } from '@entities/user';
import { Link, useNavigate, useSearch } from '@tanstack/react-router';
import c from 'clsx';
import { type FC, lazy, Suspense } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import { activeAgreementsApi } from '../../api';
import { ActiveAgreementsSkeleton } from '../ActiveAgreementsSkeleton';
import { AgreementPaymentConfirmDialog } from '../agreement-payemnt-confirm-dialog/AgreementPaymentConfirmDialog';

const ActiveAgreementDialog = lazy(() =>
  import('../active-agreement-dialog').then((module) => ({
    default: module.ActiveAgreementDialog,
  })),
);

type ActiveAgreementsListProps = {
  className?: string;
};

export const ActiveAgreementsList: FC<ActiveAgreementsListProps> = ({
  className,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.agreements);

  const { agreementReferenceKey, agreementPaymentConfirm } = useSearch({
    strict: false,
  });

  const navigate = useNavigate();

  const { data: creditAccount } = useUserCreditAccount();

  const { data: applicationAgreementsData, isPending } =
    activeAgreementsApi.useSuspenseActiveApplicationAgreementsQuery(undefined, {
      select: (data) => ({
        agreements: data?.me?.active_applications?.length
          ? data.me.active_applications
          : null,
        hasReducedMonthlyPaymentApplication:
          !!data?.me?.active_applications?.some(
            (application) =>
              !!application?.can_manually_convert_to_credit_account,
          ),
      }),
    });

  const onConfirmModalOpenChange = (state: boolean) => {
    if (!state) {
      navigate({
        to: ROUTE_NAMES.current,
        replace: true,
        search: (prev) => ({
          ...prev,
          agreementPaymentConfirm: undefined,
          agreementReferenceKey: undefined,
        }),
      });
    }
  };

  const onAgreementModalOpenChange = (state: boolean) => {
    if (!state) {
      navigate({
        to: ROUTE_NAMES.current,
        replace: true,
        search: (prev) => ({
          ...prev,
          agreementReferenceKey: undefined,
        }),
      });
    }
  };

  if (isPending) {
    return <ActiveAgreementsSkeleton />;
  }

  if (!applicationAgreementsData.agreements?.length) {
    return null;
  }

  return (
    <div className={c('grid gap-6 px-6 pt-6 pb-12 md:p-0', className)}>
      <Typography variant="xs" tag="h2">
        {t(LOCIZE_AGREEMENTS_KEYS.dashboardTitle)}
      </Typography>
      {creditAccount?.isActive ||
      applicationAgreementsData.hasReducedMonthlyPaymentApplication ? (
        <div className="space-y-6">
          {creditAccount?.isActive ? (
            <Notification localStorageKey="credit-line-notification">
              <Trans
                components={{
                  creditLineLink: (
                    <Link to={ROUTE_NAMES.creditLine} className="underline" />
                  ),
                }}
                i18nKey={LOCIZE_AGREEMENTS_KEYS.creditLineNotification}
                t={t}
              />
            </Notification>
          ) : null}
          {applicationAgreementsData.hasReducedMonthlyPaymentApplication ? (
            <Notification localStorageKey="reduce-monthly-payment">
              {t(LOCIZE_AGREEMENTS_KEYS.reduceMonthlyPaymentNotification)}
            </Notification>
          ) : null}
        </div>
      ) : null}
      <div className="grid gap-4">
        {applicationAgreementsData.agreements?.map((application) =>
          application ? (
            <ApplicationAgreement
              key={application.id}
              canManuallyConvertToCreditAccount={
                application.can_manually_convert_to_credit_account
              }
              merchantLogoSrc={application.merchant?.logo_path}
              merchantName={application.merchant?.name}
              convertingScheduleName={
                application.merchant?.campaign?.converting_schedule_name
              }
              requestedAmount={application.requested_amount}
              scheduleType={application.schedule_type}
              unpaidPrincipal={application.unpaid_principal}
              referenceKey={application.reference_key}
            />
          ) : null,
        )}
      </div>

      <SmallDialog
        open={!!agreementReferenceKey && !agreementPaymentConfirm}
        onOpenChange={onAgreementModalOpenChange}
      >
        <Suspense>
          {agreementReferenceKey ? (
            <ActiveAgreementDialog
              agreementReferenceKey={agreementReferenceKey}
            />
          ) : null}
        </Suspense>
      </SmallDialog>

      <SmallDialog
        open={!!agreementPaymentConfirm}
        onOpenChange={onConfirmModalOpenChange}
      >
        <Suspense>
          {agreementPaymentConfirm ? <AgreementPaymentConfirmDialog /> : null}
        </Suspense>
      </SmallDialog>
    </div>
  );
};
