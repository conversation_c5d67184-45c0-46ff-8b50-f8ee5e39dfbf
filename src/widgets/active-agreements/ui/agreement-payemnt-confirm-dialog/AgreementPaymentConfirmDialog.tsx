import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import {
  SmallDialogContent,
  SmallDialogFooter,
  SmallDialogHeader,
} from '@components/ui/small-dialog';
import { LOCIZE_AGREEMENTS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { AppPaymentMethod } from '@entities/payments';
import { AgreementPaymentFormView } from '@pages/agreement-payment/config';
import { getRouteApi, useSearch } from '@tanstack/react-router';
import { useTranslation } from 'react-i18next';

const routerApi = getRouteApi('/_protected/_main');

export const AgreementPaymentConfirmDialog = () => {
  const navigate = routerApi.useNavigate();
  const { agreementReferenceKey } = useSearch({ strict: false });
  const { t } = useTranslation(LOCIZE_NAMESPACES.agreements);

  const onInvoicesClick = () => {
    navigate({
      search: (prev) => ({
        ...prev,
        agreementPaymentConfirm: undefined,
        agreementReferenceKey: undefined,
      }),
    });
    setTimeout(() => {
      navigate({
        to: ROUTE_NAMES.invoices,
        search: (prev) => ({
          ...prev,
        }),
      });
    });
  };

  const onConfirmClick = () => {
    navigate({
      search: (prev) => ({
        ...prev,
        agreementPaymentConfirm: undefined,
        agreementReferenceKey: undefined,
      }),
    });
    setTimeout(() => {
      navigate({
        to: ROUTE_NAMES.paymentAgreement,
        search: (prev) => ({
          ...prev,
          referenceKey: agreementReferenceKey,
          paymentMethod: AppPaymentMethod.BANKLINK,
          formView: AgreementPaymentFormView.OVERVIEW,
        }),
      });
    });
  };

  return (
    <SmallDialogContent className="!animate-none">
      <SmallDialogHeader>
        <Typography variant="s" affects="semibold">
          {t(LOCIZE_AGREEMENTS_KEYS.confirmModalTitle)}
        </Typography>

        <Typography
          variant="text-l"
          affects="medium"
          className="text-neutral-500"
        >
          {t(LOCIZE_AGREEMENTS_KEYS.confirmModalDescription)}
        </Typography>
      </SmallDialogHeader>
      <SmallDialogFooter>
        <Button variant="black" onClick={onInvoicesClick}>
          {t(LOCIZE_AGREEMENTS_KEYS.confirmModalInvoicesBtn)}
        </Button>
        <Button variant="white" className="!mt-4" onClick={onConfirmClick}>
          {t(LOCIZE_AGREEMENTS_KEYS.confirmModalConfirmBtn)}
        </Button>
      </SmallDialogFooter>
    </SmallDialogContent>
  );
};
