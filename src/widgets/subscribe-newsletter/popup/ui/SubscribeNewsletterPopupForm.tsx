import { PhoneInput } from '@components/PhoneInput';
import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { FormControl, FormField, FormItem } from '@components/ui/form';
import { Input } from '@components/ui/input';
import {
  LOCIZE_COMMON_KEYS,
  LOCIZE_DASHBOARD_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { NewsletterSubscribeFormWrapper } from '@features/newsletter';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

type SubscribeNewsletterPopupProps = {
  onCompleted: () => void;
};

export const SubscribeNewsletterPopupForm: FC<
  SubscribeNewsletterPopupProps
> = ({ onCompleted }) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.dashboard);
  const { t: tc } = useTranslation(LOCIZE_NAMESPACES.common);

  return (
    <div className="grid gap-1">
      <Typography className="text-left">
        {t(LOCIZE_DASHBOARD_KEYS.newsletterPopupFillInfoDisclaimer)}
      </Typography>
      <NewsletterSubscribeFormWrapper
        onCompleted={onCompleted}
        className="grid gap-2"
      >
        {({ form, isAuthorized }) => (
          <>
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      disabled={form.formState.isSubmitting}
                      placeholder={tc(LOCIZE_COMMON_KEYS.emailPlaceholder)}
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {isAuthorized && (
              <FormField
                control={form.control}
                name="phone"
                render={({ field: { onChange, ...field } }) => (
                  <FormItem>
                    <FormControl>
                      <PhoneInput
                        disabled={form.formState.isSubmitting}
                        onValueChange={({ value }) => {
                          onChange(value);
                        }}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            )}

            <Button
              fullWidth
              loading={form.formState.isSubmitting}
              type="submit"
              className="mt-5"
            >
              {t(LOCIZE_DASHBOARD_KEYS.newsletterPopupSubmitButton)}
            </Button>
          </>
        )}
      </NewsletterSubscribeFormWrapper>
    </div>
  );
};
