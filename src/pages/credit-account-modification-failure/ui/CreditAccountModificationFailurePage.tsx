import { ActionCard } from '@components/actionCard/ActionCard';
import { Button } from '@components/ui/button';
import {
  LOCIZE_CREDIT_ACCOUNT_MODIFICATION_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import CancelIcon from '@icons/close.svg?react';
import { getRouteApi, useNavigate } from '@tanstack/react-router';
import { useTranslation } from 'react-i18next';

const routerApi = getRouteApi(
  '/_protected/credit-account-modification/failure',
);

export const CreditAccountModificationFailurePage = () => {
  const { hash } = routerApi.useSearch();

  const navigate = useNavigate();

  const { t } = useTranslation(LOCIZE_NAMESPACES.creditAccountModification);

  const onCtaButtonClick = () => {
    navigate({
      to: ROUTE_NAMES.creditAccountModification,
      search: {
        hash,
      },
      replace: true,
    });
  };

  return (
    <ActionCard
      icon={<CancelIcon />}
      title={t(LOCIZE_CREDIT_ACCOUNT_MODIFICATION_KEYS.failureTitle)}
      description={t(
        LOCIZE_CREDIT_ACCOUNT_MODIFICATION_KEYS.failureDescription,
      )}
      after={
        <Button onClick={onCtaButtonClick} className="w-full">
          {t(LOCIZE_CREDIT_ACCOUNT_MODIFICATION_KEYS.tryAgain)}
        </Button>
      }
    />
  );
};
