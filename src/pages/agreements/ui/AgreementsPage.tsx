import { Helmet } from '@components/Helmet';
import { Notification } from '@components/Notification';
import { Typography } from '@components/typography';
import { LOCIZE_AGREEMENTS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { agreementsApi } from '@entities/agreements/api';
import { useUserCreditAccount, useUserId } from '@entities/user';
import { Link } from '@tanstack/react-router';
import { ActiveAgreementsList } from '@widgets/active-agreements';
import { activeAgreementsApi } from '@widgets/active-agreements/api';
import { LoanOffers, LoanOffersSkeleton } from '@widgets/loan-offers';
import { PastAgreementsList } from '@widgets/past-agreements';
import c from 'clsx';
import { Suspense } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import { ApplicationStatus } from '@/shared/types';

export const AgreementsPage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.agreements);

  const { data: applicationAgreementsData } =
    activeAgreementsApi.useSuspenseActiveApplicationAgreementsQuery(undefined, {
      select: (data) =>
        data?.me?.active_applications?.length
          ? data.me.active_applications
          : null,
    });

  const { data: userId } = useUserId();
  const { data: creditAccount } = useUserCreditAccount();

  const { data: pastApplicationAgreements } =
    agreementsApi.useSuspenseUserAgreementsQuery(
      { userId, limit: 10 },
      {
        select: (data) => {
          return data.user_applications?.data?.filter(
            (application) => application?.status === ApplicationStatus.ENDED,
          );
        },
      },
    );

  const areNoAgreements =
    !pastApplicationAgreements?.length && !applicationAgreementsData?.length;

  return (
    <div className="flex flex-col md:p-12 md:pb-20">
      <Helmet title={t(LOCIZE_AGREEMENTS_KEYS.pageTitle)} />

      {areNoAgreements ? (
        <div className="px-6 pt-6 md:p-0">
          <Typography variant="m">{t(LOCIZE_AGREEMENTS_KEYS.none)}</Typography>
          {creditAccount?.isActive ? (
            <div className="mt-10 space-y-4">
              <Notification localStorageKey="credit-line-notification">
                <Trans
                  components={{
                    creditLineLink: (
                      <Link to={ROUTE_NAMES.creditLine} className="underline" />
                    ),
                  }}
                  i18nKey={LOCIZE_AGREEMENTS_KEYS.creditLineNotification}
                  t={t}
                />
              </Notification>
            </div>
          ) : null}
        </div>
      ) : (
        <>
          <ActiveAgreementsList />
          <PastAgreementsList
            className={!applicationAgreementsData?.length ? '' : 'mt-20'}
          />
        </>
      )}
      {pastApplicationAgreements?.length === 0 ||
      applicationAgreementsData?.length === 0 ? (
        <Suspense
          fallback={
            <div
              className={c(
                'mt-20',
                creditAccount?.isActive && 'mt-10',
                areNoAgreements && 'mt-10',
              )}
            >
              <LoanOffersSkeleton />
            </div>
          }
        >
          <LoanOffers
            className={c(
              'mt-20',
              creditAccount?.isActive && 'mt-10',
              areNoAgreements && 'mt-10',
            )}
          />
        </Suspense>
      ) : null}
    </div>
  );
};
