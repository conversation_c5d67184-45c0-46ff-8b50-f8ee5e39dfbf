import { ApplicationScheduleType, ContractType } from '@/shared/types';

export const CONTRACT_TYPE_BY_SCHEDULE_TYPE_MAP: Record<
  ApplicationScheduleType,
  ContractType
> = {
  [ApplicationScheduleType.ESTO_PAY]: ContractType.APPLICATION_SIGNED,
  [ApplicationScheduleType.ESTO_X]: ContractType.APPLICATION_SIGNED,
  [ApplicationScheduleType.PAY_LATER]: ContractType.APPLICATION_SIGNED,
  [ApplicationScheduleType.REGULAR]: ContractType.APPLICATION_SIGNED,
  [ApplicationScheduleType.FAST_LOAN]: ContractType.SMALL_LOAN_SIGNED,
  [ApplicationScheduleType.RENOVATION_LOAN]: ContractType.SMALL_LOAN_SIGNED,
  [ApplicationScheduleType.SMALL_LOAN]: ContractType.SMALL_LOAN_SIGNED,
  [ApplicationScheduleType.VEHICLE_LOAN]: ContractType.SMALL_LOAN_SIGNED,
  [ApplicationScheduleType.TRAVEL_LOAN]: ContractType.SMALL_LOAN_SIGNED,
  [ApplicationScheduleType.HEALTH_LOAN]: ContractType.SMALL_LOAN_SIGNED,
  [ApplicationScheduleType.BEAUTY_LOAN]: ContractType.SMALL_LOAN_SIGNED,
};
