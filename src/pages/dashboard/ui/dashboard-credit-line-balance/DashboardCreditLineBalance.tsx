import { APP_CONFIG } from '@config/app';
import { APP_COUNTRY } from '@config/envs';
import { LOCIZE_DASHBOARD_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import VerifiedMarkIcon from '@icons/verified-mark.svg?react';
import {
  CreditLineBalance,
  CreditLineBalanceState,
  useCreditLineBalanceState,
} from '@widgets/credit-line-balance';
import type { FC } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import { SupportedCountries } from '@/shared/types';

const NO_SIGNED_BONUSES = {
  [SupportedCountries.EE]: [
    LOCIZE_DASHBOARD_KEYS.noSignedCaBonus1,
    LOCIZE_DASHBOARD_KEYS.noSignedCaBonus2,
    LOCIZE_DASHBOARD_KEYS.noSignedCaBonus3,
  ],
  [SupportedCountries.LT]: [
    LOCIZE_DASHBOARD_KEYS.noSignedCaBonus1,
    LOCIZE_DASHBOARD_KEYS.noSignedCaBonus2,
  ],
  [SupportedCountries.LV]: [
    LOCIZE_DASHBOARD_KEYS.noSignedCaBonus2,
    LOCIZE_DASHBOARD_KEYS.noSignedCaBonus3,
  ],
} as const satisfies Record<SupportedCountries, Array<string>>;

export const DashboardCreditLineBalance: FC = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.dashboard);
  const creditLineBalanceState = useCreditLineBalanceState();

  return (
    <div className="grid gap-6 px-6 pt-8 pb-12 md:p-0">
      <CreditLineBalance />
      {!APP_CONFIG.creditLine.isCreditAccountInterestFreeBannerEnabled &&
        creditLineBalanceState === CreditLineBalanceState.NO_SIGNED && (
          <div className="px-2 mt-2 flex gap-6 flex-wrap min-[485px]:justify-center md:mb-0">
            {NO_SIGNED_BONUSES[APP_COUNTRY].map((bonus) => (
              <div key={bonus} className="flex items-center gap-2">
                <VerifiedMarkIcon className="size-5 text-system-green" />
                <div>
                  <Trans
                    t={t}
                    i18nKey={bonus}
                    components={{ b: <b className="font-semibold" /> }}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
    </div>
  );
};
