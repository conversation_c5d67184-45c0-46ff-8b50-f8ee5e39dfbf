import { Button } from '@components/ui/button';
import { LOCIZE_DASHBOARD_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { userApi, useUserNewsletterSubscriptionInfo } from '@entities/user';
import { useToast } from '@hooks/system';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { cn } from '@utils/tailwind';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

export const NewsletterSubscribeButton: FC = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.dashboard);

  const navigate = useNavigate();

  const { showErrorMessage } = useToast();
  const queryClient = useQueryClient();
  const { data: userNewsletterSubscriptionInfo } =
    useUserNewsletterSubscriptionInfo();

  const onNotEnoughDataError = () => {
    navigate({
      to: ROUTE_NAMES.current,
      search: {
        showNewsletterPopup: true,
      },
    });
  };

  const updateUserNewsletterAgreementMutation = userApi.useUpdateUserMutation({
    onSuccess: (data) => {
      if (!data.update_user?.id) {
        showErrorMessage();
        return;
      }
      queryClient.invalidateQueries({
        queryKey: userApi.useSuspenseUserQuery.getKey(),
      });
    },
    onError: () => {
      showErrorMessage();
    },
  });

  const subscribeToNewsletter = async () => {
    if (!userNewsletterSubscriptionInfo?.id)
      throw new Error('Cannot subscribe to newsletter without user');

    const { email, phone, id } = userNewsletterSubscriptionInfo;
    if (!email || !phone) {
      onNotEnoughDataError();
      return;
    }

    await updateUserNewsletterAgreementMutation.mutateAsync({
      email,
      phone,
      userId: id,
      newsletterAgreement: true,
    });
  };

  return (
    <div className="w-fit">
      <Button
        loading={updateUserNewsletterAgreementMutation.isPending}
        onClick={subscribeToNewsletter}
        className={cn(
          updateUserNewsletterAgreementMutation.isSuccess && 'animate-fade-out',
          updateUserNewsletterAgreementMutation.isPending && 'w-[6rem]',
        )}
        size="small"
      >
        {updateUserNewsletterAgreementMutation.isSuccess
          ? t(LOCIZE_DASHBOARD_KEYS.newsletterButtonSuccess)
          : t(LOCIZE_DASHBOARD_KEYS.newsletterButtonDefault)}
      </Button>
    </div>
  );
};
