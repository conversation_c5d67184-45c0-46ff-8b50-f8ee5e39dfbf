import { ActionCard } from '@components/actionCard/ActionCard';
import { Typography } from '@components/typography';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_CREDIT_ACCOUNT_MODIFICATION_KEYS } from '@config/locize/credit-account-modification';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { CreditAccountModificationSigningButton } from './CreditAccountModificationSigningButton';

type CreditAccountModificationLowerOrSameLimitViewProps = {
  contractUrl: string;
};

export const CreditAccountModificationLowerOrSameLimitView: FC<
  CreditAccountModificationLowerOrSameLimitViewProps
> = ({ contractUrl }) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditAccountModification);

  return (
    <ActionCard
      title={t(
        LOCIZE_CREDIT_ACCOUNT_MODIFICATION_KEYS.creditAccountModificationTitle,
      )}
      description={t(
        LOCIZE_CREDIT_ACCOUNT_MODIFICATION_KEYS.creditAccountModificationDescription2,
      )}
      before={
        contractUrl ? (
          <Typography className="[&>a]:text-neutral-500 [&>a]:underline">
            <a href={contractUrl} target="_blank" rel="noreferrer">
              {t(
                LOCIZE_CREDIT_ACCOUNT_MODIFICATION_KEYS.creditAccountModificationContractLink,
              )}
            </a>
          </Typography>
        ) : null
      }
      after={<CreditAccountModificationSigningButton />}
    />
  );
};
