import { ActionCard } from '@components/actionCard/ActionCard';
import { Typography } from '@components/typography';
import {
  LOCIZE_CREDIT_ACCOUNT_MODIFICATION_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { useGetCreditAccountLimitRecalculationData } from '@entities/credit-account-limit-recalculation/hooks';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { CreditAccountModificationSigningButton } from './CreditAccountModificationSigningButton';

type CreditAccountModificationHigherLimitViewProps = {
  contractUrl: string;
  hash: string;
};

export const CreditAccountModificationHigherLimitView: FC<
  CreditAccountModificationHigherLimitViewProps
> = ({ contractUrl, hash }) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditAccountModification);

  const { data } = useGetCreditAccountLimitRecalculationData(hash);

  return (
    <ActionCard
      classNames={{ title: 'text-system-green' }}
      title={t(
        LOCIZE_CREDIT_ACCOUNT_MODIFICATION_KEYS.creditAccountModificationIncreaseAmount,
        {
          increaseAmount: data?.creditLimitIncreaseAmount,
        },
      )}
      description={t(
        LOCIZE_CREDIT_ACCOUNT_MODIFICATION_KEYS.creditAccountModificationDescription,
        {
          newCreditLimit: data?.newCreditLimit,
        },
      )}
      before={
        contractUrl ? (
          <Typography className="[&>a]:text-neutral-500 [&>a]:underline">
            <a href={contractUrl} target="_blank" rel="noreferrer">
              {t(
                LOCIZE_CREDIT_ACCOUNT_MODIFICATION_KEYS.creditAccountModificationContractLink,
              )}
            </a>
          </Typography>
        ) : null
      }
      after={<CreditAccountModificationSigningButton />}
    />
  );
};
