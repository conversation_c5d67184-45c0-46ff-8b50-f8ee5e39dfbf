import { ActionCard } from '@components/actionCard/ActionCard';
import { Button } from '@components/ui/button';
import {
  LOCIZE_CREDIT_ACCOUNT_LIMIT_INCREASE_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import CancelIcon from '@icons/close.svg?react';
import { getRouteApi, useNavigate } from '@tanstack/react-router';
import { useTranslation } from 'react-i18next';

const routerApi = getRouteApi(
  '/_protected/credit-account-limit-increase/failure',
);

export const CreditAccountLimitIncreaseFailurePage = () => {
  const { hash } = routerApi.useSearch();

  const navigate = useNavigate();

  const { t } = useTranslation(LOCIZE_NAMESPACES.creditAccountLimitIncrease);

  const onCtaButtonClick = () => {
    navigate({
      to: ROUTE_NAMES.creditAccountLimitIncrease,
      search: {
        hash,
      },
      replace: true,
    });
  };

  return (
    <ActionCard
      icon={<CancelIcon />}
      title={t(
        LOCIZE_CREDIT_ACCOUNT_LIMIT_INCREASE_KEYS.creditAccountLimitIncreaseFailureTitle,
      )}
      description={t(
        LOCIZE_CREDIT_ACCOUNT_LIMIT_INCREASE_KEYS.creditAccountLimitIncreaseFailureDescription,
      )}
      after={
        <Button onClick={onCtaButtonClick} className="w-full">
          {t(
            LOCIZE_CREDIT_ACCOUNT_LIMIT_INCREASE_KEYS.creditAccountLimitIncreaseTryAgain,
          )}
        </Button>
      }
    />
  );
};
