import { InfoTooltip } from '@components/InfoTooltip';
import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { Dialog, DialogContent } from '@components/ui/dialog';
import { Skeleton } from '@components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@components/ui/table';
import {
  LOCIZE_CREDIT_LINE_PAGE_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { OLD_APP_ROUTE_NAME, ROUTE_NAMES } from '@config/routes';
import { AppPaymentMethod } from '@entities/payments';
import { useUserCreditAccount } from '@entities/user';
import { useFeatureToggles } from '@hooks/system';
import DownloadIcon from '@icons/download.svg?react';
import { useCreditAccountContracts } from '@pages/credit-line/hooks/useCreditAccountContracts';
import { generateCreditAccountContractDownloadUrl } from '@pages/credit-line/utils';
import { CreditLinePaymentFormView } from '@pages/credit-line-payment/config';
import { getRouteApi } from '@tanstack/react-router';
import { formatDate, formatNumber } from '@utils/formatters';
import { type FC, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ContractType } from '@/shared/types';

const routeApi = getRouteApi('/_protected/_main/credit-line');

export const CreditLinePersonalInfo: FC = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditLinePage);
  const { data: creditAccount } = useUserCreditAccount();
  if (!creditAccount) throw new Error('Credit account is not found');

  const navigate = routeApi.useNavigate();
  const [isMakePaymentDialogOpen, setIsMakePaymentDialogOpen] = useState(false);
  const [isRedirectiongToPayment, setIsRedirectiongToPayment] = useState(false);
  const featureToggles = useFeatureToggles();

  const detailsData: Array<{
    label: string;
    value: string;
    info?: string;
  }> = [
    {
      label: LOCIZE_CREDIT_LINE_PAGE_KEYS.detailsCreditLimit,
      value: `${formatNumber({ value: creditAccount.creditLimit })} €`,
    },
    {
      label: LOCIZE_CREDIT_LINE_PAGE_KEYS.detailsCreditUsed,
      value: `${formatNumber({ value: creditAccount.unpaidPrincipal })} €`,
    },
    {
      label: LOCIZE_CREDIT_LINE_PAGE_KEYS.detailsAvailableToWithdraw,
      value: `${formatNumber({
        value: creditAccount.creditLimit - creditAccount.unpaidPrincipal,
      })} €`,
    },
  ];

  const redirectToInvoices = () => {
    if (featureToggles.invoicesFeature) {
      navigate({
        to: ROUTE_NAMES.invoices,
        replace: true,
      });
      return;
    }

    window.open(OLD_APP_ROUTE_NAME.pay, '_self');
  };

  const payCreditAccount = async () => {
    setIsRedirectiongToPayment(true);

    try {
      await navigate({
        to: ROUTE_NAMES.paymentCreditLine,
        replace: true,
        search: {
          creditAccountHash: creditAccount.hash,
          paymentMethod: AppPaymentMethod.BANKLINK,
          formView: CreditLinePaymentFormView.OVERVIEW,
        },
      });
    } finally {
      setIsRedirectiongToPayment(false);
    }
  };

  return (
    <>
      <div className="grid gap-10">
        <div className="grid gap-6">
          <Typography tag="h2" variant="xs">
            {t(LOCIZE_CREDIT_LINE_PAGE_KEYS.detailsHeading)}
          </Typography>
          <div className="grid gap-6">
            <div className="border border-neutral-200 rounded-[.875rem] divide-y-[1px] divide-neutral-200">
              {detailsData.map((item) => (
                <div
                  className="p-4 flex items-center justify-between gap-2"
                  key={item.label}
                >
                  {item.info ? (
                    <div className="flex items-center gap-2">
                      <Typography variant="text-s">{t(item.label)}</Typography>
                      <InfoTooltip
                        text={t(item.info)}
                        className="max-w-[400px]"
                      />
                    </div>
                  ) : (
                    <Typography variant="text-s">{t(item.label)}</Typography>
                  )}
                  <Typography variant="text-s">{item.value}</Typography>
                </div>
              ))}
            </div>
            {!!creditAccount.totalOutstanding && (
              <Button
                size="small"
                variant="grey"
                className="w-max"
                onClick={() => setIsMakePaymentDialogOpen(true)}
              >
                {t(LOCIZE_CREDIT_LINE_PAGE_KEYS.detailsMakePaymentButton)}
              </Button>
            )}
          </div>
        </div>
        <CreditAccountContractsSection />
      </div>
      <Dialog
        open={isMakePaymentDialogOpen}
        onOpenChange={setIsMakePaymentDialogOpen}
      >
        <DialogContent className="sm:!rounded-3xl size-full max-w-none rounded-none border-none sm:h-auto sm:max-w-[25rem] sm:p-8">
          <div className="mt-24 flex flex-col text-center md:mt-14">
            <Typography variant="s" className="mb-6">
              {t(LOCIZE_CREDIT_LINE_PAGE_KEYS.makePaymentDialogTitle)}
            </Typography>
            <Typography
              variant="text-l"
              className="mb-12 md:mb-10"
              affects="semibold"
            >
              {t(LOCIZE_CREDIT_LINE_PAGE_KEYS.makePaymentDialogDescription)}
            </Typography>
            <div className="grid gap-4">
              <Button onClick={redirectToInvoices}>
                {t(
                  LOCIZE_CREDIT_LINE_PAGE_KEYS.makePaymentDialogInvoicesButton,
                )}
              </Button>
              <Button
                variant="white"
                loading={isRedirectiongToPayment}
                onClick={payCreditAccount}
              >
                {t(LOCIZE_CREDIT_LINE_PAGE_KEYS.makePaymentDialogSubmitButton)}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

function CreditAccountContractsSection() {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditLinePage);
  const { data: creditAccountContracts, isFetching } =
    useCreditAccountContracts();

  return (
    <div className="grid gap-6">
      <Typography tag="h2" variant="xs">
        {t(LOCIZE_CREDIT_LINE_PAGE_KEYS.documentsHeading)}
      </Typography>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>
              {t(LOCIZE_CREDIT_LINE_PAGE_KEYS.documentsTableHeadSigned)}
            </TableHead>
            <TableHead>
              {t(LOCIZE_CREDIT_LINE_PAGE_KEYS.documentsTableHeadType)}
            </TableHead>
            <TableHead className="w-5">
              <DownloadIcon className="text-neutral-500" />
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isFetching ? (
            <TableRow>
              <TableCell>
                <Skeleton className="h-[1.25rem] w-24" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-[1.25rem] w-24" />
              </TableCell>
              <TableCell>
                <Skeleton className="size-5" />
              </TableCell>
            </TableRow>
          ) : (
            creditAccountContracts?.map((contract) =>
              contract ? (
                <TableRow key={contract.url}>
                  <TableCell>
                    {formatDate(contract.signed_at * 1_000)}
                  </TableCell>
                  <TableCell>
                    {contract.type === ContractType.CREDIT_ACCOUNT_PAYMENT_LEAVE
                      ? t(
                          LOCIZE_CREDIT_LINE_PAGE_KEYS.documentsTableContractTypeGracePeriod,
                        )
                      : t(
                          LOCIZE_CREDIT_LINE_PAGE_KEYS.documentsTableContractTypeDefault,
                        )}
                  </TableCell>
                  <TableCell>
                    <a
                      href={generateCreditAccountContractDownloadUrl({
                        creditAccountId: contract.credit_account_id ?? -1,
                        url: contract.url,
                      })}
                      target="_blank"
                      rel="noreferrer"
                    >
                      <DownloadIcon className="hover:text-neutral-700" />
                    </a>
                  </TableCell>
                </TableRow>
              ) : null,
            )
          )}
        </TableBody>
      </Table>
    </div>
  );
}
