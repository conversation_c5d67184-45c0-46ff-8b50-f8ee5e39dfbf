import { Typography } from '@components/typography';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@components/ui/accordion';
import { Button } from '@components/ui/button';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_CREDIT_LINE_PAGE_KEYS } from '@config/locize';
import { useAppConfig } from '@hooks/system';
import {
  CREDIT_LINE_FAQ,
  CREDIT_LINE_HOW_IT_WORKS,
} from '@pages/credit-line/config';
import { cn } from '@utils/tailwind';
import type { FC } from 'react';
import { Trans, useTranslation } from 'react-i18next';

type CreditLineGeneralInfoProps = {
  className?: string;
};

export const CreditLineGeneralInfo: FC<CreditLineGeneralInfoProps> = ({
  className,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditLinePage);

  const { supportUrl } = useAppConfig();

  return (
    <div className={cn('grid gap-10 md:gap-[4.5rem]', className)}>
      <div className="grid gap-6">
        <Typography tag="h2" variant="xs">
          {t(LOCIZE_CREDIT_LINE_PAGE_KEYS.howItWorksHeading)}
        </Typography>
        <div className="grid gap-8 md:grid-cols-2">
          {CREDIT_LINE_HOW_IT_WORKS.map((item) => (
            <div
              key={item.title}
              className="bg-primary-white rounded-2xl p-6 grid gap-4 border border-neutral-200"
            >
              <img src={item.imageSrc} alt={t(item.title)} />
              <Typography tag="h3" variant="xxs">
                {t(item.title)}
              </Typography>
              <Typography variant="text-s" className="text-neutral-500">
                {t(item.description)}
              </Typography>
            </div>
          ))}
        </div>
      </div>
      <div className="grid gap-6">
        <Typography tag="h2" variant="xs">
          {t(LOCIZE_CREDIT_LINE_PAGE_KEYS.faqHeading)}
        </Typography>
        <div className="grid gap-4">
          {CREDIT_LINE_FAQ.map((item) => (
            <Accordion
              key={item.question}
              type="multiple"
              className="grid gap-4"
            >
              <AccordionItem value={t(item.question)}>
                <AccordionTrigger>{t(item.question)}</AccordionTrigger>
                <AccordionContent>
                  <Trans
                    t={t}
                    i18nKey={item.answer}
                    components={{ br: <br /> }}
                  />
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          ))}
          <div
            className="flex items-center justify-between gap-6 p-6 rounded-2xl border border-neutral-200 bg-primary-white"
            style={{
              boxShadow: '0px 4px 10px 0px rgba(51, 37, 109, 0.07)',
            }}
          >
            <Typography>
              {t(LOCIZE_CREDIT_LINE_PAGE_KEYS.helpdeskLabel)}
            </Typography>
            <Button size="small" asChild>
              <a href={supportUrl} target="_blank" rel="noreferrer">
                {t(LOCIZE_CREDIT_LINE_PAGE_KEYS.helpdeskLink)}
              </a>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
