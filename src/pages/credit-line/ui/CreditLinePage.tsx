import { Helmet } from '@components/Helmet';
import {
  LOCIZE_CREDIT_LINE_PAGE_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { CreditLineCardSkeleton } from '@entities/credit-line';
import { CreditLinePageContent } from '@pages/credit-line/ui/CreditLinePageContent';
import { CreditLineBalance } from '@widgets/credit-line-balance';
import { Suspense } from 'react';
import { useTranslation } from 'react-i18next';

import { CreditLineBlockSkeleton } from './credit-line-block-skeleton';

export const CreditLinePage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditLinePage);

  return (
    <>
      <Helmet title={t(LOCIZE_CREDIT_LINE_PAGE_KEYS.pageTitle)} />
      <div className="grid gap-0 md:gap-[4.5rem] md:p-12 md:pb-20">
        <Suspense fallback={<CreditLineCardSkeleton />}>
          <div className="px-8 pt-12 pb-10 border-neutral-200 border-b md:p-0 md:border-none">
            <CreditLineBalance isCreditLinePage />
          </div>
        </Suspense>
        <div className="grid gap-10">
          <Suspense fallback={<CreditLineBlockSkeleton />}>
            <CreditLinePageContent />
          </Suspense>
        </div>
      </div>
    </>
  );
};
