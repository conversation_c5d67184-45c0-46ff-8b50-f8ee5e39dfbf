import { Typography } from '@components/typography';
import { Separator } from '@components/ui/separator';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_GRACE_PERIOD_KEYS } from '@config/locize/grace-period';
import { useAppConfig } from '@hooks/system';
import { paymentLeavePricingKeys } from '@pages/grace-period/config';
import { formatNumber } from '@utils/formatters';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { useGracePeriodAgreementInfo } from '../../hooks/ useGracePeriodAgreementInfo';
import { usePaymentLeavePricingKeysValues } from './hooks';

export const GracePeriodInfo: FC = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.gracePeriod);

  const { agreementUrl, fee } = useGracePeriodAgreementInfo();

  const { termsUrl } = useAppConfig();

  const { data: paymentLeaveKeys } = usePaymentLeavePricingKeysValues();

  return (
    <div className="grid w-full p-4 bg-neutral-50 space-y-4 rounded-[.875rem] max-w-[25rem] md:rounded-2xl md:bg-primary-white md:p-8">
      <div className="grid gap-2">
        <Typography variant="text-s" tag="h2" affects="semibold">
          {t(LOCIZE_GRACE_PERIOD_KEYS.infoTitle1)}
        </Typography>
        <Typography variant="text-s" className="text-neutral-500">
          {paymentLeaveKeys?.[paymentLeavePricingKeys.APPLICATION_INTEREST]
            ? t(LOCIZE_GRACE_PERIOD_KEYS.infoDescription2)
            : t(LOCIZE_GRACE_PERIOD_KEYS.infoDescription1)}
        </Typography>
      </div>
      <Separator decorative className="text-neutral-200 md:opacity-0" />
      <div className="grid gap-2">
        <Typography variant="text-s" tag="h2" affects="semibold">
          {t(LOCIZE_GRACE_PERIOD_KEYS.infoTitle2)}
        </Typography>
        <Typography variant="text-s" className="text-neutral-500">
          {paymentLeaveKeys?.[paymentLeavePricingKeys.CREDIT_ACCOUNT_INTEREST]
            ? t(LOCIZE_GRACE_PERIOD_KEYS.infoDescription2)
            : t(LOCIZE_GRACE_PERIOD_KEYS.infoDescription1)}
        </Typography>
      </div>
      <Separator decorative className="text-neutral-200 md:opacity-0" />
      <div className="grid gap-2">
        <Typography variant="text-s" tag="h2" affects="semibold">
          {t(LOCIZE_GRACE_PERIOD_KEYS.infoTitle3)}
        </Typography>
        <Typography variant="text-s" className="text-neutral-500">
          {t(LOCIZE_GRACE_PERIOD_KEYS.infoDescription3, {
            fee: formatNumber({ value: fee }),
          })}
        </Typography>
      </div>
      <Separator decorative className="text-neutral-200 md:opacity-0" />
      <Typography className="[&>a]:underline">
        <a href={agreementUrl} target="_blank" rel="noreferrer">
          {t(LOCIZE_GRACE_PERIOD_KEYS.agreementLink)}
        </a>
        ,{' '}
        <a href={termsUrl} target="_blank" rel="noreferrer">
          {t(LOCIZE_GRACE_PERIOD_KEYS.conditionsLink)}
        </a>
      </Typography>
    </div>
  );
};
