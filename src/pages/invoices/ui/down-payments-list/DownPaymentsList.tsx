import { Typography } from '@components/typography';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@components/ui/table';
import { LOCIZE_INVOICES_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { AppPaymentMethod } from '@entities/payments';
import { useGetDownPayments } from '@pages/invoices/hooks/useGetDownPayments';
import { Link } from '@tanstack/react-router';
import { formatDate, formatNumber } from '@utils/formatters';
import { useTranslation } from 'react-i18next';

export const DownPaymentsList = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.invoices);

  const { unpaidDownPayments } = useGetDownPayments();

  if (unpaidDownPayments.length === 0) return null;

  return (
    <div className="border-neutral-200 shadow-container rounded-[.875rem] border p-4 flex flex-col gap-2">
      <div className="flex items-center gap-4 justify-between pb-4">
        <Typography variant="xxs" tag="h2">
          {t(LOCIZE_INVOICES_KEYS.downPaymentsTitle)}
        </Typography>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t(LOCIZE_INVOICES_KEYS.dueDate)}</TableHead>
            <TableHead>{t(LOCIZE_INVOICES_KEYS.amount)}</TableHead>

            <TableHead className="w-5">
              {t(LOCIZE_INVOICES_KEYS.downPaymentsPayment)}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {unpaidDownPayments.map((installment) => (
            <TableRow key={installment?.id}>
              <TableCell>{formatDate(installment?.due_at)}</TableCell>
              <TableCell>
                {formatNumber({ value: installment?.unpaid_amount })} €
              </TableCell>

              <TableCell>
                <Link
                  to={ROUTE_NAMES.paymentAgreement}
                  search={{
                    paymentMethod: AppPaymentMethod.BANKLINK,
                    referenceKey: installment?.application?.reference_key,
                    amount: installment?.unpaid_amount,
                  }}
                  className="underline underline-offset-[3.5px]"
                >
                  {t(LOCIZE_INVOICES_KEYS.payInvoiceLink)}
                </Link>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
