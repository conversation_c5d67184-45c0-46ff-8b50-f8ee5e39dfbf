import { Cta, CtaSkeleton } from '@components/Cta';
import { LOCIZE_INVOICES_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { useUserRecentInvoice } from '@entities/invoices';
import { useUserId } from '@entities/user';
import { useGracePeriodApply } from '@features/grace-period-apply';
import { useGracePeriodAgreement } from '@pages/invoices/hooks/useGracePeriodAgreement';
import { useStandingPaymentAgreement } from '@pages/invoices/hooks/useStandingPaymentAgreement';
import { getRouteApi } from '@tanstack/react-router';
import { formatDate } from '@utils/formatters';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

const routeApi = getRouteApi('/_protected/_main/invoices');

export const InvoiceAgreements: FC = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.invoices);

  const navigate = routeApi.useNavigate();
  const { data: userId } = useUserId();
  const { data: userRecentInvoice } = useUserRecentInvoice();
  const {
    data: standingPaymentAgreement,
    isFetching: isStandingPaymentAgreementFetching,
  } = useStandingPaymentAgreement();
  const {
    data: gracePeriodAgreement,
    isFetching: isGracePeriodAgreementFetching,
  } = useGracePeriodAgreement({
    enabled: !!userRecentInvoice,
  });
  const gracePeriodApply = useGracePeriodApply();

  const handleGracePeriodCtaClick = () => {
    if (!gracePeriodAgreement)
      throw new Error('Grace period agreement is missing');

    if (gracePeriodAgreement.isActive) {
      navigate({
        to: ROUTE_NAMES.gracePeriodSuccess,
      });
      return;
    }

    gracePeriodApply.mutate({ userId });
  };

  const handleStandingPaymentCtaClick = () => {
    if (!standingPaymentAgreement)
      throw new Error('Standing payment agreement is missing');

    if (standingPaymentAgreement.isActive) {
      navigate({
        to: ROUTE_NAMES.standingPaymentDisable,
        search: (search) => ({
          ...search,
          fromPathname: location.pathname,
        }),
      });
      return;
    }

    navigate({
      to: ROUTE_NAMES.standingPaymentEnable,
      search: (search) => ({
        ...search,
        fromPathname: location.pathname,
      }),
    });
  };

  const getGracePeriodCtaText = () => {
    if (gracePeriodAgreement?.endAt) {
      return t(LOCIZE_INVOICES_KEYS.gracePeriodCtaEndAt, {
        endAt: formatDate(gracePeriodAgreement.endAt),
      });
    }

    return gracePeriodAgreement?.isActive
      ? t(LOCIZE_INVOICES_KEYS.gracePeriodCta)
      : t(LOCIZE_INVOICES_KEYS.gracePeriodCtaApply);
  };

  if (isGracePeriodAgreementFetching || isStandingPaymentAgreementFetching)
    return (
      <div className="-mt-2 flex gap-4 flex-col md:flex-row md:items-center">
        <CtaSkeleton className="flex-1" />
        <CtaSkeleton className="flex-1" />
      </div>
    );

  if (!gracePeriodAgreement && !standingPaymentAgreement) return null;

  return (
    <div className="-mt-2 flex gap-4 flex-col md:flex-row md:items-center">
      {gracePeriodAgreement?.isAllowed ? (
        <Cta
          text={getGracePeriodCtaText()}
          loading={gracePeriodApply.isPending}
          actionButton
          onClick={handleGracePeriodCtaClick}
          indicatorProps={{
            variant: gracePeriodAgreement.isActive ? 'green' : 'gray',
            isAnimated: false,
          }}
          className="flex-1"
        />
      ) : null}
      {standingPaymentAgreement?.isAllowed ? (
        <Cta
          text={t(LOCIZE_INVOICES_KEYS.standingPaymentCta)}
          actionButton
          onClick={handleStandingPaymentCtaClick}
          indicatorProps={{
            variant: standingPaymentAgreement.isActive ? 'green' : 'gray',
            isAnimated: false,
          }}
          className="flex-1"
        />
      ) : null}
    </div>
  );
};
