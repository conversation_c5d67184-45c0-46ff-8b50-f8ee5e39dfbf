import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@components/ui/table';
import { LOCIZE_INVOICES_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { useUserRecentInvoice } from '@entities/invoices';
import { AppPaymentMethod } from '@entities/payments';
import { userApi } from '@entities/user';
import DownloadIcon from '@icons/download.svg?react';
import { Link } from '@tanstack/react-router';
import { formatDate, formatNumber } from '@utils/formatters';
import { useTranslation } from 'react-i18next';

export const RecentInvoiceBlock = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.invoices);

  const { data: referenceKey } = userApi.useSuspenseUserQuery(undefined, {
    select: (data) => {
      if (!data?.me?.reference_key) {
        throw new Error("User's reference key is missing while paying invoice");
      }

      return data.me.reference_key;
    },
  });

  const { data } = useUserRecentInvoice();

  if (!data) return null;

  if (data.isPaid)
    return (
      <div className="border-neutral-200 shadow-container rounded-[.875rem] border p-4 flex flex-col min-w-full gap-2">
        <Typography variant="xxs" tag="h2">
          {t(LOCIZE_INVOICES_KEYS.noInvoicesTitle)}
        </Typography>
        <Typography variant="text-s" className="text-neutral-500">
          {t(LOCIZE_INVOICES_KEYS.noInvoicesDescription)}
        </Typography>
      </div>
    );

  return (
    <div className="border-neutral-200 shadow-container rounded-[.875rem] border p-4 flex flex-col min-w-full gap-2">
      <div className="flex items-center gap-4 justify-between pb-4">
        <Typography variant="xxs" tag="h2">
          {t(LOCIZE_INVOICES_KEYS.newInvoiceTitle)}
        </Typography>
        <Button size="small" variant="blue" asChild>
          <Link
            to={ROUTE_NAMES.paymentInvoice}
            search={{
              paymentMethod: AppPaymentMethod.BANKLINK,
              referenceKey,
            }}
          >
            {t(LOCIZE_INVOICES_KEYS.payInvoiceLink)}
          </Link>
        </Button>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t(LOCIZE_INVOICES_KEYS.dueDate)}</TableHead>
            <TableHead>{t(LOCIZE_INVOICES_KEYS.amount)}</TableHead>
            {data.url ? (
              <TableHead className="w-5">
                <DownloadIcon className="text-neutral-500" />
              </TableHead>
            ) : null}
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell>{formatDate(data.nextPaymentDate)}</TableCell>
            <TableCell>{formatNumber({ value: data.totalAmount })} €</TableCell>
            {data.url ? (
              <TableCell>
                <a href={data.url} target="_blank" rel="noreferrer">
                  <DownloadIcon className="hover:text-neutral-700" />
                </a>
              </TableCell>
            ) : null}
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};
