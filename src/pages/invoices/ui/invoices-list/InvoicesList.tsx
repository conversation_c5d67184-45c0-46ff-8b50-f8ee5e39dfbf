import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { Skeleton } from '@components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@components/ui/table';
import { LOCIZE_INVOICES_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import DownloadIcon from '@icons/download.svg?react';
import { useInvoices } from '@pages/invoices/hooks/useInvoices';
import { formatDate, formatNumber } from '@utils/formatters';
import { Fragment } from 'react';
import { useTranslation } from 'react-i18next';

export const InvoicesList = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.invoices);

  const {
    data,
    isPending,
    isSuccess,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  } = useInvoices();

  if (isSuccess && !data.pages?.[0]?.invoices?.total) return null;

  return (
    <div className="flex flex-col gap-2 min-w-full items-start">
      <Typography variant="xs" tag="h2" className="mb-4">
        {t(LOCIZE_INVOICES_KEYS.allInvoicesTitle)}
      </Typography>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t(LOCIZE_INVOICES_KEYS.dueDate)}</TableHead>
            <TableHead>{t(LOCIZE_INVOICES_KEYS.amount)}</TableHead>
            <TableHead className="w-5">
              <DownloadIcon className="text-neutral-500" />
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isPending ? (
            <InvoicesTableBodySkeleton />
          ) : (
            data?.pages?.map((page, i) => (
              <Fragment key={`invoices-page-${i.toString()}`}>
                {page.invoices?.data?.map((invoice) => {
                  if (!invoice) return null;

                  return (
                    <TableRow key={invoice.id}>
                      <TableCell>{formatDate(invoice.due_at)}</TableCell>
                      <TableCell>
                        {formatNumber({ value: invoice.total_amount })} €
                      </TableCell>
                      {invoice.url ? (
                        <TableCell>
                          <a
                            href={invoice.url}
                            target="_blank"
                            rel="noreferrer"
                          >
                            <DownloadIcon className="hover:text-neutral-700" />
                          </a>
                        </TableCell>
                      ) : null}
                    </TableRow>
                  );
                })}
              </Fragment>
            ))
          )}
        </TableBody>
      </Table>
      {hasNextPage ? (
        <Button
          onClick={() => fetchNextPage()}
          loading={isFetchingNextPage}
          variant="grey"
          className="mt-6"
        >
          {t(LOCIZE_INVOICES_KEYS.loadMore)}
        </Button>
      ) : null}
    </div>
  );
};

function InvoicesTableBodySkeleton() {
  return Array.from({ length: 3 }).map((_, rowIndex) => (
    <TableRow key={`invoice-row-${rowIndex.toString()}`}>
      {Array.from({ length: 2 }).map((_, cellIndex) => (
        <TableCell key={`invoice-cell-${(cellIndex + rowIndex).toString()}`}>
          <Skeleton className="h-[1.25rem] w-28" />
        </TableCell>
      ))}
      <TableCell key={`invoice-cell-button-${rowIndex.toString()}`}>
        <Skeleton className="size-5" />
      </TableCell>
    </TableRow>
  ));
}
