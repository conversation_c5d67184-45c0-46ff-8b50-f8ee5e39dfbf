import { Helmet } from '@components/Helmet';
import { Typography } from '@components/typography';
import { LOCIZE_INVOICES_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useHasUserInvoices } from '@pages/invoices/hooks/useHasUserInvoices';
import { LoanOffersSkeleton } from '@widgets/loan-offers';
import { lazy, Suspense } from 'react';
import { useTranslation } from 'react-i18next';

import { useGetDownPayments } from '../hooks/useGetDownPayments';
import { DownPaymentsList } from './down-payments-list/DownPaymentsList';
import { InvoiceAgreements } from './invoice-agreements';
import { InvoicesList } from './invoices-list';
import { RecentInvoiceBlock } from './recent-invoice-block';

const LoanOffers = lazy(() =>
  import('@widgets/loan-offers').then((module) => ({
    default: module.LoanOffers,
  })),
);

export const InvoicesPage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.invoices);

  const { hasUserInvoices, isPending } = useHasUserInvoices();

  const { unpaidDownPayments } = useGetDownPayments();

  const userHasNoInvoices =
    !isPending && !hasUserInvoices && unpaidDownPayments.length === 0;

  return (
    <div className={'grid gap-10 p-6 md:p-12'}>
      <Helmet title={t(LOCIZE_INVOICES_KEYS.pageTitle)} />

      {userHasNoInvoices ? (
        <>
          <Typography variant="m" tag="h2">
            {t(LOCIZE_INVOICES_KEYS.noInvoicesTitle)}
          </Typography>
          <Suspense fallback={<LoanOffersSkeleton />}>
            <LoanOffers />
          </Suspense>
        </>
      ) : (
        <>
          <RecentInvoiceBlock />
          <DownPaymentsList />
          <InvoiceAgreements />
          <InvoicesList />
        </>
      )}
    </div>
  );
};
