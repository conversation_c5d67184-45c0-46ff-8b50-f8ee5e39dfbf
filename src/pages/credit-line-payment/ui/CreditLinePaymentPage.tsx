import { Helmet } from '@components/Helmet';
import { LateSuspense } from '@components/LateSuspense';
import { LoadingSpinner } from '@components/LoadingSpinner';
import { Button } from '@components/ui/button';
import {
  LOCIZE_CREDIT_LINE_PAYMENT_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { AppPaymentMethod } from '@entities/payments';
import { useIsUserAuthorized } from '@entities/user';
import {
  type PaymentMethodsSelectProps,
  PaymentMethodsToggleGroup,
} from '@features/payment-methods-select';
import { useIsMobileView } from '@hooks/system';
import { getRouteApi, Link } from '@tanstack/react-router';
import { PaymentAuthHeader } from '@widgets/headers/payment-auth-header';
import { DualPanelLayout } from '@widgets/layouts/dual-panel';
import { lazy, type ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

import { CreditLinePaymentFormView } from '../config';
import { useCreditLinePaymentCalculation } from '../hooks/useCreditLinePaymentCalculation';
import { CreditLinePaymentInfo } from './credit-line-payment-info';

const CreditLinePaymentManualTransferInfo = lazy(() =>
  import('./credit-line-payment-manual-transfer-info').then(
    ({ CreditLinePaymentManualTransferInfo }) => ({
      default: CreditLinePaymentManualTransferInfo,
    }),
  ),
);
const BanklinkPaymentForm = lazy(() =>
  import('@widgets/banklink-payment').then(({ BanklinkPaymentForm }) => ({
    default: BanklinkPaymentForm,
  })),
);
const CreditCardPaymentSelect = lazy(() =>
  import('@features/credit-card-payment').then(
    ({ CreditCardPaymentSelect }) => ({
      default: CreditCardPaymentSelect,
    }),
  ),
);

const routeApi = getRouteApi('/payment/credit-line/');

const paymentMethodRenderer: Record<
  AppPaymentMethod,
  (data: {
    creditAccountId?: number;
    amount: number;
    redirectUrl: string;
    onCancel?: () => void;
  }) => ReactNode
> = {
  [AppPaymentMethod.BANKLINK]: (data) => <BanklinkPaymentForm {...data} />,
  [AppPaymentMethod.CARD]: (data) => <CreditCardPaymentSelect {...data} />,
  [AppPaymentMethod.MANUAL_TRANSFER]: ({ onCancel }) => (
    <CreditLinePaymentManualTransferInfo onCancel={onCancel} />
  ),
};

export const CreditLinePaymentPage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditLinePayment);

  const isMobileView = useIsMobileView();
  const { data: isAuthorized } = useIsUserAuthorized();

  return (
    <>
      <Helmet title={t(LOCIZE_CREDIT_LINE_PAYMENT_KEYS.pageTitle)} />
      <DualPanelLayout
        fromPage={ROUTE_NAMES.creditLine}
        header={!isAuthorized ? <PaymentAuthHeader /> : undefined}
        left={isMobileView ? null : <CreditLinePaymentInfo />}
        right={<CreditLinePaymentRightPanel />}
      />
    </>
  );
};

function CreditLinePaymentRightPanel() {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditLinePayment);
  const { data: payment } = useCreditLinePaymentCalculation();
  const isMobileView = useIsMobileView();
  const { paymentMethod, formView } = routeApi.useSearch();

  const navigate = routeApi.useNavigate();
  const { creditAccountId } = routeApi.useRouteContext();

  const onPaymentMethodChange: PaymentMethodsSelectProps['onValueChange'] = (
    value,
  ) => {
    navigate({
      replace: true,
      search: (prev) => ({
        ...prev,
        paymentMethod: value,
      }),
    });
  };

  const onCancelPaymentMethod = () => {
    navigate({
      replace: true,
      search: (prev) => ({
        ...prev,
        formView: CreditLinePaymentFormView.OVERVIEW,
      }),
    });
  };

  return (
    <div className="w-full max-w-[25rem] flex flex-col h-full">
      <div className="flex flex-col md:p-0 md:flex-1 md:justify-center md:max-h-[689px]">
        <LateSuspense fallback={<LoadingSpinner className="mx-auto" />}>
          {formView === CreditLinePaymentFormView.OVERVIEW ? (
            <>
              {isMobileView ? (
                <>
                  <CreditLinePaymentInfo />
                  <Button className="mt-14" asChild>
                    <Link
                      to={ROUTE_NAMES.current}
                      search={(prev) => ({
                        ...prev,
                        formView: CreditLinePaymentFormView.PAYMENT,
                      })}
                    >
                      {t(LOCIZE_CREDIT_LINE_PAYMENT_KEYS.paymentMethod)}
                    </Link>
                  </Button>
                </>
              ) : (
                <>
                  <PaymentMethodsToggleGroup
                    className="mb-8"
                    value={paymentMethod}
                    onValueChange={onPaymentMethodChange}
                  />
                  {payment?.card_payment_fee ? (
                    paymentMethodRenderer[paymentMethod]({
                      creditAccountId,
                      amount: payment.card_payment_fee.amount,
                      redirectUrl: `${window.origin}${ROUTE_NAMES.paymentCreditLine}`,
                    })
                  ) : (
                    // TODO: Add loading state
                    <LoadingSpinner className="mx-auto" />
                  )}
                </>
              )}
            </>
          ) : payment?.card_payment_fee ? (
            <>
              <PaymentMethodsToggleGroup
                className="mb-8"
                value={paymentMethod}
                onValueChange={onPaymentMethodChange}
              />
              {paymentMethodRenderer[paymentMethod]({
                creditAccountId,
                amount: payment.card_payment_fee.amount,
                redirectUrl: `${window.origin}${ROUTE_NAMES.paymentCreditLine}`,
                onCancel: isMobileView ? onCancelPaymentMethod : undefined,
              })}
            </>
          ) : (
            // TODO: Add loading state
            <LoadingSpinner className="mx-auto" />
          )}
        </LateSuspense>
      </div>
    </div>
  );
}
