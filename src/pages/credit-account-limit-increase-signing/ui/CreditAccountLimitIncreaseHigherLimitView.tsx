import { ActionCard } from '@components/actionCard/ActionCard';
import { Typography } from '@components/typography';
import {
  LOCIZE_CREDIT_ACCOUNT_LIMIT_INCREASE_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { useGetCreditAccountLimitRecalculationData } from '@entities/credit-account-limit-recalculation/hooks';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { CreditAccountLimitIncreaseSigningButton } from './CreditAccountLimitIncreaseSigningButton';

type CreditAccountLimitIncreaseHigherLimitViewProps = {
  contractUrl: string;
  hash: string;
};

export const CreditAccountLimitIncreaseHigherLimitView: FC<
  CreditAccountLimitIncreaseHigherLimitViewProps
> = ({ contractUrl, hash }) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditAccountLimitIncrease);

  const { data } = useGetCreditAccountLimitRecalculationData(hash);

  return (
    <ActionCard
      classNames={{ subtitle: 'text-system-green', titleVariant: 'xs' }}
      title={t(
        LOCIZE_CREDIT_ACCOUNT_LIMIT_INCREASE_KEYS.creditAccountLimitIncreaseTitle,
      )}
      subtitle={t(
        LOCIZE_CREDIT_ACCOUNT_LIMIT_INCREASE_KEYS.creditAccountLimitIncreaseAmount,
        {
          increaseAmount: data?.creditLimitIncreaseAmount,
        },
      )}
      description={t(
        LOCIZE_CREDIT_ACCOUNT_LIMIT_INCREASE_KEYS.creditAccountLimitIncreaseDescription,
        {
          newCreditLimit: data?.newCreditLimit,
        },
      )}
      before={
        contractUrl ? (
          <Typography className="text-neutral-500 underline">
            <a href={contractUrl} target="_blank" rel="noreferrer">
              {t(
                LOCIZE_CREDIT_ACCOUNT_LIMIT_INCREASE_KEYS.creditAccountLimitIncreaseContractLink,
              )}
            </a>
          </Typography>
        ) : null
      }
      after={<CreditAccountLimitIncreaseSigningButton />}
    />
  );
};
