import { ActionCard } from '@components/actionCard/ActionCard';
import { Typography } from '@components/typography';
import {
  LOCIZE_CREDIT_ACCOUNT_LIMIT_INCREASE_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { CreditAccountLimitIncreaseSigningButton } from './CreditAccountLimitIncreaseSigningButton';

type CreditAccountLimitIncreaseLowerOrSameLimitViewProps = {
  contractUrl: string;
};

export const CreditAccountLimitIncreaseLowerOrSameLimitView: FC<
  CreditAccountLimitIncreaseLowerOrSameLimitViewProps
> = ({ contractUrl }) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditAccountLimitIncrease);

  return (
    <ActionCard
      title={t(
        LOCIZE_CREDIT_ACCOUNT_LIMIT_INCREASE_KEYS.creditAccountLimitIncreaseTitle,
      )}
      description={t(
        LOCIZE_CREDIT_ACCOUNT_LIMIT_INCREASE_KEYS.creditAccountLimitIncreaseDescription2,
      )}
      before={
        contractUrl ? (
          <Typography className="text-neutral-500 underline">
            <a href={contractUrl} target="_blank" rel="noreferrer">
              View contract
            </a>
          </Typography>
        ) : null
      }
      after={<CreditAccountLimitIncreaseSigningButton />}
    />
  );
};
