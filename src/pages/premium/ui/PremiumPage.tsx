import { Callout } from '@components/Callout';
import { Helmet } from '@components/Helmet';
import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { Dialog, DialogContent } from '@components/ui/dialog';
import {
  LOCIZE_COMMON_KEYS,
  LOCIZE_NAMESPACES,
  LOCIZE_PREMIUM_PAGE_KEYS,
} from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { useUserRecentInvoice } from '@entities/invoices/hooks/useUserRecentInvoice';
import { AppPaymentMethod } from '@entities/payments';
import { premiumSubscriptionApi } from '@entities/premium';
import { usePremiumSubscriptionPrice } from '@entities/premium/hooks';
import { userApi, useUserId, useUserPremiumSubscription } from '@entities/user';
import { useAutoAnimate } from '@formkit/auto-animate/react';
import { PREMIUM_FEATURES } from '@pages/premium/config';
import { useQueryClient } from '@tanstack/react-query';
import { getRouteApi, Outlet, useRouterState } from '@tanstack/react-router';
import { Link } from '@tanstack/react-router';
import { formatDate } from '@utils/formatters';
import { Suspense, useCallback, useMemo } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import { PremiumSubscriptionStatus } from '@/shared/types';

const routerApi = getRouteApi('/_protected/_main/premium');

export const PremiumPage = () => {
  const { data: userId } = useUserId();

  const { t } = useTranslation(LOCIZE_NAMESPACES.premiumPage);
  const { t: tc } = useTranslation(LOCIZE_NAMESPACES.common);
  const navigate = routerApi.useNavigate();
  const {
    location: { pathname },
  } = useRouterState();

  const [parent] = useAutoAnimate();

  const { data: referenceKey } = userApi.useSuspenseUserQuery(undefined, {
    select: (data) => {
      if (!data?.me?.reference_key) {
        throw new Error("User's reference key is missing");
      }

      return data.me.reference_key;
    },
  });

  const { data: premiumSubscriptionPrice } = usePremiumSubscriptionPrice();
  const { data: userPremiumSubscription } = useUserPremiumSubscription();
  const { data: userInvoice } = useUserRecentInvoice();

  const queryClient = useQueryClient();
  const isOpenModal = useMemo(() => {
    return (
      ROUTE_NAMES.premiumSubscribe === pathname ||
      ROUTE_NAMES.premiumUnsubscribe === pathname
    );
  }, [pathname]);

  const subscribeToPremiumMutation =
    premiumSubscriptionApi.useSubscribeToPremiumMutation({
      onSuccess: async () => {
        await queryClient.invalidateQueries({
          queryKey: userApi.useSuspenseUserPremiumSubscriptionQuery.getKey(),
        });
      },
    });

  const handleOpenModalClick = useCallback(() => {
    if (!userPremiumSubscription) {
      navigate({
        to: ROUTE_NAMES.premiumSubscribe,
      });
      return;
    }

    navigate({
      to: ROUTE_NAMES.premiumUnsubscribe,
    });
  }, [userPremiumSubscription, navigate]);

  const calloutContent = useMemo(() => {
    if (userInvoice?.isOverdue) {
      return t(LOCIZE_PREMIUM_PAGE_KEYS.calloutOverdue);
    }

    if (
      userPremiumSubscription?.premiumSubscriptionStatus ===
        PremiumSubscriptionStatus.ACTIVE_UNSUBSCRIBED &&
      userPremiumSubscription?.endAt
    ) {
      return (
        <Trans
          components={{
            date: <>{formatDate(userPremiumSubscription.endAt)}</>,
          }}
          i18nKey={LOCIZE_PREMIUM_PAGE_KEYS.calloutUnsubscribed}
          t={t}
        />
      );
    }

    return null;
  }, [
    userInvoice?.isOverdue,
    userPremiumSubscription?.premiumSubscriptionStatus,
    userPremiumSubscription?.endAt,
    t,
  ]);

  const handleResubscribeButtonClick = useCallback(() => {
    subscribeToPremiumMutation.mutate({
      ownerId: userId,
    });
  }, [userId, subscribeToPremiumMutation.mutate]);

  const actionButton = useMemo(() => {
    if (userInvoice?.isOverdue) {
      return (
        <Button size="small" variant="blue" asChild>
          <Link
            to={ROUTE_NAMES.paymentInvoice}
            search={{
              paymentMethod: AppPaymentMethod.BANKLINK,
              referenceKey,
              withPremium:
                userPremiumSubscription?.premiumSubscriptionStatus ===
                PremiumSubscriptionStatus.NO_ACTIVE_SUBSCRIPTION,
            }}
          >
            {t(LOCIZE_PREMIUM_PAGE_KEYS.buttonCtaOverdue)}
          </Link>
        </Button>
      );
    }

    if (
      userPremiumSubscription?.premiumSubscriptionStatus ===
      PremiumSubscriptionStatus.ACTIVE_UNSUBSCRIBED
    ) {
      return (
        <Button
          variant="blue"
          loading={subscribeToPremiumMutation.isPending}
          onClick={handleResubscribeButtonClick}
        >
          {t(LOCIZE_PREMIUM_PAGE_KEYS.buttonCtaResubscribe)}
        </Button>
      );
    }

    return (
      <Button
        variant={userPremiumSubscription ? 'grey' : 'blue'}
        onClick={handleOpenModalClick}
      >
        {userPremiumSubscription
          ? t(LOCIZE_PREMIUM_PAGE_KEYS.buttonCtaUnsubscribe)
          : t(LOCIZE_PREMIUM_PAGE_KEYS.buttonCtaSubscribe)}
      </Button>
    );
  }, [
    handleOpenModalClick,
    userPremiumSubscription,
    userInvoice?.isOverdue,
    subscribeToPremiumMutation.isPending,
    t,
    handleResubscribeButtonClick,
    referenceKey,
  ]);

  return (
    <>
      <Helmet title={t(LOCIZE_PREMIUM_PAGE_KEYS.pageTitle)} />
      <div className="flex flex-col py-10 px-6 md:p-12 md:items-center">
        <Typography variant="m" tag="h1" className="mb-4">
          {t(LOCIZE_PREMIUM_PAGE_KEYS.pageTitle)}
        </Typography>
        <Typography tag="p" className="mb-6">
          {premiumSubscriptionPrice}€ / {tc(LOCIZE_COMMON_KEYS.monthLabel)}.{' '}
          {t(LOCIZE_PREMIUM_PAGE_KEYS.cancelDisclaimer)}.
        </Typography>
        <div className="flex flex-col gap-6 py-6 mb-6 md:mb-8 md:flex-row">
          {PREMIUM_FEATURES.map((feature) => (
            <div
              key={feature.title}
              className="flex flex-col gap-4 md:max-w-[17.5rem] md:text-center md:items-center"
            >
              <div className="size-12 rounded-full bg-neutral-50 flex justify-center items-center text-primary-brand02">
                <feature.icon />
              </div>
              <Typography variant="xs" tag="h2">
                {t(feature.title)}
              </Typography>
              <Typography tag="p">{t(feature.description)}</Typography>
            </div>
          ))}
        </div>
        <div ref={parent}>
          {!!calloutContent && (
            <Callout text={calloutContent} className="mb-8 max-w-[27rem]" />
          )}
        </div>

        {actionButton}
      </div>

      <Dialog
        open={isOpenModal}
        onOpenChange={(state) => {
          if (!state) {
            navigate({
              to: ROUTE_NAMES.premium,
            });
          }
        }}
      >
        <DialogContent className="size-full flex justify-center p-8 !max-w-none">
          <div className="pt-20 max-w-[25rem] overflow-auto">
            <Suspense>
              <Outlet />
            </Suspense>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
