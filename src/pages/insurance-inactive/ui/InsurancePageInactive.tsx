import { Helmet } from '@components/Helmet';
import { LOCIZE_INSURANCE_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { InsuranceInfo } from '@features/insurance-info';
import { useTranslation } from 'react-i18next';

export const InsurancePageInactive = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.insurance);

  return (
    <div className="flex flex-col px-6 pt-6 pb-10 md:px-12 md:pt-12 md:pb-20">
      <Helmet title={t(LOCIZE_INSURANCE_KEYS.insurancePageTitle)} />
      <InsuranceInfo bannerVisible getStartedButtonVisible />
    </div>
  );
};
