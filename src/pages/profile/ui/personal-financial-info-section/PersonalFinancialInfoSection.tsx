import { InfoTooltip } from '@components/InfoTooltip';
import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_PROFILE_PAGE_KEYS } from '@config/locize/profile-page';
import { UserProfileFormFields } from '@config/profile/form';
import { ROUTE_NAMES } from '@config/routes';
import { userApi, useUserPremiumSubscription } from '@entities/user';
import { useAppConfig, useIsCountry } from '@hooks/system';
import CrownIcon from '@icons/crown.svg?react';
import { FORM_FIELDS_PER_COUNTRY } from '@pages/profile/config';
import { Link } from '@tanstack/react-router';
import { getFullName } from '@utils/getFullName';
import { useTranslation } from 'react-i18next';

import { SupportedCountries } from '@/shared/types';

const parseValue = (value?: Nullable<string | number>) =>
  typeof value === 'number' ? value : value || '-';

export const PersonalFinancialInfoSection = () => {
  const { country } = useAppConfig();
  const { t } = useTranslation(LOCIZE_NAMESPACES.profilePage);
  const { data: userProfileData } = userApi.useSuspenseUserProfileInfoQuery();
  const { data: userData } = userApi.useSuspenseUserQuery();
  const isLtRegion = useIsCountry(SupportedCountries.LT);

  const { personalFields, financialFields } = FORM_FIELDS_PER_COUNTRY[country];

  const profileInfo: Array<{
    id: number;
    title: string;
    description?: string;
    items: Array<{
      label: string;
      tooltip?: string;
      value: string | number;
      name: UserProfileFormFields;
    }>;
  }> = [
    {
      id: 1,
      title: LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.personalInfoTitle,
      items: [
        {
          name: UserProfileFormFields.PHONE,
          label: LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.phoneLabel,
          value: parseValue(userData?.me?.phone),
        },
        {
          name: UserProfileFormFields.EMAIL,
          label: LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.emailLabel,
          value: parseValue(userData?.me?.email),
        },
        {
          name: UserProfileFormFields.ADDRESS,
          label: LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.addressLabel,
          value: parseValue(userProfileData?.me?.profile?.address),
        },
        {
          name: UserProfileFormFields.POST_CODE,
          label: LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.postCodeLabel,
          value: parseValue(userProfileData?.me?.profile?.post_code),
        },
        {
          name: UserProfileFormFields.CITY,
          label: LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.cityLabel,
          value: parseValue(userProfileData?.me?.profile?.city),
        },
        {
          name: UserProfileFormFields.POLITICAL_EXPOSURE,
          label:
            LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
              .politicalExposureLabel,
          value: userProfileData?.me?.political_exposure
            ? t(
                LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                  .politicalExposure[userProfileData?.me?.political_exposure],
              )
            : '-',
          tooltip:
            LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
              .politicalExposureTooltip,
        },
      ].filter(({ name }) =>
        personalFields.some((fieldName) => fieldName === name),
      ),
    },
    {
      id: 2,
      title: LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.financialInfoTitle,
      description:
        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.financialInfoDescription,
      items: [
        {
          name: UserProfileFormFields.OCCUPATION_CATEGORY,
          label: LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.occupationLabel,
          value: userProfileData?.me?.profile?.occupation_category
            ? t(
                LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.occupation[
                  userProfileData?.me?.profile.occupation_category
                ],
              )
            : '-',
        },
        {
          name: UserProfileFormFields.EMPLOYMENT_DATE,
          label:
            LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.employmentDateLabel,
          value: parseValue(userProfileData?.me?.profile?.employment_date),
        },
        {
          name: UserProfileFormFields.IBAN,
          label: LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.ibanLabel,
          value: parseValue(userProfileData?.me?.profile?.iban),
          tooltip: LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.ibanTooltip,
        },
        {
          name: UserProfileFormFields.NET_INCOME_MONTHLY,
          label: isLtRegion
            ? LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                .netIncomeMonthlyLabelLt
            : LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                .netIncomeMonthlyLabel,
          value: parseValue(userProfileData?.me?.profile?.net_income_monthly),
          tooltip: isLtRegion
            ? LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                .netIncomeMonthlyTooltipLt
            : LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                .netIncomeMonthlyTooltip,
        },
        {
          name: UserProfileFormFields.MONTHLY_LIVING_EXPENSES,
          label:
            LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
              .monthlyLivingExpensesLabel,
          value: parseValue(
            userProfileData?.me?.profile?.monthly_living_expenses,
          ),
          tooltip:
            LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
              .monthlyLivingExpensesTooltip,
        },
        {
          name: UserProfileFormFields.EXPENDITURE_MONTHLY,
          label:
            LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
              .expenditureMonthlyLabel,
          value: parseValue(userProfileData?.me?.profile?.expenditure_monthly),
        },
        {
          name: UserProfileFormFields.OVERDUE_DEBT,
          label:
            LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.overdueDebtLabel,
          value: userProfileData?.me?.profile?.overdue_debt
            ? t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.yes)
            : t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.no),
        },
        {
          name: UserProfileFormFields.PLANNING_NEW_DEBTS,
          label:
            LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
              .planningNewDebtsLabel,
          value: userProfileData?.me?.profile?.planning_new_debts
            ? t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.yes)
            : t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.no),
        },
        {
          name: UserProfileFormFields.NUMBER_OF_DEPENDENTS,
          label:
            LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
              .numberOfDependentsLabel,
          value: parseValue(userProfileData?.me?.profile?.number_of_dependents),
        },
        {
          name: UserProfileFormFields.FUTURE_REDUCED_EARNINGS,
          label:
            LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
              .futureReducedEarningsLabel,
          value: userProfileData?.me?.profile?.future_reduced_earnings
            ? t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.yes)
            : t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.no),
        },
        {
          name: UserProfileFormFields.ULTIMATE_BENEFICIAL_OWNER,
          label:
            LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
              .ultimateBeneficialOwnerLabel,
          value: userProfileData?.me?.profile?.ultimate_beneficial_owner
            ? t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.yes)
            : t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.no),
          tooltip:
            LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
              .ultimateBeneficialOwnerTooltip,
        },
      ].filter(({ name }) =>
        financialFields.some((fieldName) => fieldName === name),
      ),
    },
  ];

  const { data: premiumSubscription } = useUserPremiumSubscription();
  const hasPremiumSubscription = !!premiumSubscription;

  return (
    <div className="p-6 relative md:p-8 md:border md:rounded-3xl md:border-neutral-200">
      {hasPremiumSubscription ? (
        <img
          className="absolute top-0 right-6 md:right-8"
          src="/images/profile/premium-badge.webp"
          alt="Credit Line"
        />
      ) : null}
      <div className="flex gap-4 justify-between lg:items-center items-start flex-col lg:flex-row">
        <Typography
          className="break-words hyphens-auto order-2 lg:order-1 mt-2 lg:mt-0"
          variant="s"
        >
          {getFullName({
            firstName: userData?.me?.profile?.first_name,
            lastName: userData?.me?.profile?.last_name,
            defaultValue: t(LOCIZE_PROFILE_PAGE_KEYS.unknownUserPlaceholder),
          })}
        </Typography>
        {!hasPremiumSubscription ? (
          <Link
            to={ROUTE_NAMES.premium}
            className="flex items-center gap-2 rounded-lg bg-neutral-100 px-[14px] py-[6.5px] order-1 lg:order-2 lg:transition-colors lg:hover:bg-neutral-200"
          >
            <CrownIcon className="text-neutral-500 shrink-0" width={18} />
            <Typography className="text-neutral-500">
              {t(
                LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.premiumDisabled,
              )}
            </Typography>
          </Link>
        ) : null}
      </div>
      <div className="divide-y-[1px]">
        {profileInfo.map(({ id, title, description, items }) => (
          <div className="flex flex-col gap-2 py-6" key={id}>
            <Typography variant="xs">{t(title)}</Typography>
            {description ? <Typography>{t(description)}</Typography> : null}
            <div className="mt-4 grid gap-4">
              {items.map(({ label, value, tooltip }) => (
                <div
                  className="flex flex-col justify-between gap-1 lg:flex-row lg:gap-2"
                  key={t(label)}
                >
                  {tooltip ? (
                    <div className="flex gap-2">
                      <Typography className="text-neutral-500">
                        {t(label)}
                      </Typography>
                      <InfoTooltip
                        className="max-w-[300px]"
                        text={t(tooltip)}
                      />
                    </div>
                  ) : (
                    <Typography className="text-neutral-500">
                      {t(label)}
                    </Typography>
                  )}

                  <Typography>{value}</Typography>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
      <Button asChild className="lg:mt-6" size="small">
        <Link to={ROUTE_NAMES.profileEdit}>
          {t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.editButton)}
        </Link>
      </Button>
    </div>
  );
};
