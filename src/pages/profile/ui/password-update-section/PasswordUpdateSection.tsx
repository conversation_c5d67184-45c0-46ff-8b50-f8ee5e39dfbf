import { Typography } from '@components/typography';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_PROFILE_PAGE_KEYS } from '@config/locize/profile-page';
import { PasswordUpdateForm } from '@widgets/password-update';
import { useTranslation } from 'react-i18next';

export const PasswordUpdateSection = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.profilePage);

  return (
    <div className="p-6 md:p-0 md:mt-6">
      <div className="p-6 mb-20 border-neutral-200 border rounded-3xl">
        <Typography className="mb-4" affects="bold">
          {t(LOCIZE_PROFILE_PAGE_KEYS.passwordUpdateSection.title)}
        </Typography>
        <Typography className="mb-10" variant="text-s">
          {t(LOCIZE_PROFILE_PAGE_KEYS.passwordUpdateSection.description)}
        </Typography>
        <PasswordUpdateForm />
      </div>
    </div>
  );
};
