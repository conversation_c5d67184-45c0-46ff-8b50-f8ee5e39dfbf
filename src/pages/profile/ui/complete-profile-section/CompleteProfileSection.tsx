import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { Progress } from '@components/ui/progress';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_PROFILE_PAGE_KEYS } from '@config/locize/profile-page';
import { ROUTE_NAMES } from '@config/routes';
import CheckIcon from '@icons/check.svg?react';
import ExclamationMarkIcon from '@icons/profile/exclamation-mark.svg?react';
import { Link } from '@tanstack/react-router';
import { useTranslation } from 'react-i18next';

import { useCompletedProfileData } from '../../hooks';

export const CompleteProfileSection = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.profilePage);

  const { percentageCompleted, isCompleted, completedSectionsList } =
    useCompletedProfileData();

  if (!completedSectionsList?.length || isCompleted) {
    return null;
  }

  return (
    <div className="p-6 md:p-0 border-b border-neutral-200 md:border-none md:mb-6">
      <div className="relative border-primary-black border-2 rounded-3xl p-6">
        <ExclamationMarkIcon className="absolute top-[-12px] left-[26px]" />
        <Typography tag="h2" variant="xs">
          {t(LOCIZE_PROFILE_PAGE_KEYS.completeProfile.title)}
        </Typography>
        <Typography className="mt-4" tag="p">
          {t(LOCIZE_PROFILE_PAGE_KEYS.completeProfile.description)}
        </Typography>
        <div className="flex items-center gap-[10px] mt-6">
          <Typography>
            {Number.parseInt(percentageCompleted.toString())}%
          </Typography>
          <Progress value={percentageCompleted} variant="blue" />
        </div>
        <ul className="mt-4 flex flex-col gap-4">
          {completedSectionsList.map(({ key, isCompleted }) => (
            <li
              className="grid grid-cols-[auto_1fr] items-center gap-2 [&[data-is-completed='false']>svg]:text-neutral-400 [&[data-is-completed='true']>svg]:text-primary-brand02 [&[data-is-completed='true']>span]:line-through"
              key={key}
              data-is-completed={isCompleted}
            >
              <CheckIcon />
              <Typography variant="text-m" tag="span">
                {t(key)}
              </Typography>
            </li>
          ))}
        </ul>
        <Button className="mt-8" variant="blue" asChild>
          <Link to={ROUTE_NAMES.profileComplete}>
            {t(LOCIZE_PROFILE_PAGE_KEYS.completeProfile.actionButton)}
          </Link>
        </Button>
      </div>
    </div>
  );
};
