import { Helmet } from '@components/Helmet';
import { Dialog, DialogContent } from '@components/ui/dialog';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_PROFILE_PAGE_KEYS } from '@config/locize/profile-page';
import { ROUTE_NAMES } from '@config/routes';
import { getRouteApi, Outlet, useRouterState } from '@tanstack/react-router';
import { Suspense } from 'react';
import { useTranslation } from 'react-i18next';

import { CompleteProfileSection } from './complete-profile-section';
import { PasswordUpdateSection } from './password-update-section/PasswordUpdateSection';
import { PersonalFinancialInfoSection } from './personal-financial-info-section';
import { SubscribeToNewsletterSection } from './subscribe-to-newsletter-section';

const routeApi = getRouteApi('/_protected/_main/profile');

export const ProfilePage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.profilePage);
  const navigate = routeApi.useNavigate();
  const {
    location: { pathname },
  } = useRouterState();

  const isOpenModal =
    pathname === ROUTE_NAMES.profileComplete ||
    pathname === ROUTE_NAMES.profileEdit;

  const onModalOpenChange = (state: boolean) => {
    if (!state) {
      navigate({
        to: ROUTE_NAMES.profile,
      });
    }
  };

  return (
    <div className="h-full md:p-12">
      <Helmet title={t(LOCIZE_PROFILE_PAGE_KEYS.pageTitle)} />

      <Suspense
        fallback={
          <Skeleton className="mx-6 my-6 h-[25rem] rounded-3xl md:mx-0 md:mt-0 md:w-full" />
        }
      >
        <CompleteProfileSection />
      </Suspense>

      <Suspense
        fallback={<Skeleton className="h-[90rem] w-full md:rounded-3xl" />}
      >
        <PersonalFinancialInfoSection />
      </Suspense>

      <Suspense
        fallback={<Skeleton className="h-[9rem] w-full md:rounded-3xl" />}
      >
        <SubscribeToNewsletterSection />
      </Suspense>

      <PasswordUpdateSection />

      <Dialog open={isOpenModal} onOpenChange={onModalOpenChange}>
        <DialogContent className="size-full flex justify-center p-8 !max-w-none">
          <div className="w-full overflow-auto">
            <div className="pt-20 max-w-[28.75rem] w-full mx-auto">
              <Outlet />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
