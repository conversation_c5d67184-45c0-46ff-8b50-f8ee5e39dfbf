import { Typography } from '@components/typography';
import { Table, TableBody, TableCell, TableRow } from '@components/ui/table';
import { LOCIZE_INSURANCE_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useGetInsuranceId } from '@entities/insurance/hooks/useGetInsuranceId';
import { useAppConfig } from '@hooks/system';
import DownloadIcon from '@icons/download.svg?react';
import { useTranslation } from 'react-i18next';

import {
  generateInsurancePolicyDownloadUrl,
  generateInsuranceServiceTermsDownloadUrl,
} from '../utils';

export const InsuranceDocumentsTable = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.insurance);
  const { i18n } = useTranslation();

  const insuranceId = useGetInsuranceId();

  const { seesamDataPolicyUrl } = useAppConfig();

  return (
    <div className="flex flex-col gap-4 mt-10">
      <Typography variant="xxs" affects="semibold">
        {t(LOCIZE_INSURANCE_KEYS.activeInsuranceDocuments)}
      </Typography>
      <Table>
        <TableBody>
          <TableRow>
            <TableCell>
              <Typography variant="text-s">
                {t(LOCIZE_INSURANCE_KEYS.activeInsurancePolicy)}
              </Typography>
            </TableCell>
            <TableCell className="align-right">
              <a
                target="_blank"
                href={generateInsurancePolicyDownloadUrl({
                  insuranceId,
                })}
                rel="noreferrer"
                className="flex justify-end"
              >
                <DownloadIcon className="hover:text-neutral-700" />
              </a>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>
              <Typography variant="text-s">
                {' '}
                {t(LOCIZE_INSURANCE_KEYS.activeInsuranceTerms)}
              </Typography>
            </TableCell>
            <TableCell className="text-right">
              <a
                target="_blank"
                href={generateInsuranceServiceTermsDownloadUrl({
                  language: i18n.language,
                })}
                rel="noreferrer"
                className="flex justify-end"
              >
                <DownloadIcon className="hover:text-neutral-700" />
              </a>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>
              <Typography variant="text-s">
                {t(LOCIZE_INSURANCE_KEYS.activeInsuranceDataPolicy)}
              </Typography>
            </TableCell>
            <TableCell className="text-right">
              <a
                target="_blank"
                href={seesamDataPolicyUrl}
                rel="noreferrer"
                className="flex justify-end"
              >
                <DownloadIcon className="hover:text-neutral-700" />
              </a>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};

export default InsuranceDocumentsTable;
