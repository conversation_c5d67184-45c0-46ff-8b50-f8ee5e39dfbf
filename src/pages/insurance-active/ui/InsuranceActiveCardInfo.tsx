import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_INSURANCE_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useAppConfig } from '@hooks/system';
import { lazy, Suspense } from 'react';
import { useTranslation } from 'react-i18next';

const InsuranceStatusInfo = lazy(() =>
  import('./InsuranceStatusInfo').then((module) => ({
    default: module.InsuranceStatusInfo,
  })),
);

export const InsuranceActiveCardInfo = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.insurance);

  const { seesamClaimsUrl } = useAppConfig();

  const onClaimButtonClick = () => {
    window.open(seesamClaimsUrl, '_blank');
  };

  return (
    <div className="border-neutral-200 rounded-[.875rem] border p-8 flex flex-col gap-8 mb-[2rem] md:mb-[5rem]">
      <div className="flex flex-col justify-between gap-8 md:flex-row">
        <div className="flex flex-col gap-2">
          <Typography variant="xs" affects="semibold">
            {t(LOCIZE_INSURANCE_KEYS.insurancePageTitle)}
          </Typography>
          <Typography variant="text-m">
            {t(LOCIZE_INSURANCE_KEYS.activeInsuranceDescription)}
          </Typography>
        </div>

        <Button onClick={onClaimButtonClick}>
          {t(LOCIZE_INSURANCE_KEYS.activeInsuranceClaimButton)}
        </Button>
      </div>
      <Suspense fallback={<InsuranceActiveCardInfoSkeleton />}>
        <InsuranceStatusInfo />
      </Suspense>
    </div>
  );
};

function InsuranceActiveCardInfoSkeleton() {
  return (
    <div className="flex flex-col gap-1">
      <Skeleton className="h-[4.75rem] w-full rounded-2xl md:h-[4.5rem]" />
      <Skeleton className="h-[4.75rem] w-full rounded-2xl md:h-[4.5rem]" />
    </div>
  );
}
