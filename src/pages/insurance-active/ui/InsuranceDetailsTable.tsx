import { Typography } from '@components/typography';
import { Table, TableBody, TableCell, TableRow } from '@components/ui/table';
import { LOCIZE_INSURANCE_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useGetInsurance } from '@entities/insurance/hooks/useGetInsurance';
import { formatDate } from '@utils/formatters';
import { useTranslation } from 'react-i18next';

export const InsuranceDetailsTable = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.insurance);

  const { data: insuranceData } = useGetInsurance();

  return (
    <div className="flex flex-col gap-4 mt-10">
      <Typography variant="xxs" affects="semibold">
        {t(LOCIZE_INSURANCE_KEYS.activeInsuranceDetails)}
      </Typography>
      <Table>
        <TableBody>
          <TableRow>
            <TableCell>
              <Typography variant="text-s">
                {t(LOCIZE_INSURANCE_KEYS.activeInsuranceSigned)}
              </Typography>
            </TableCell>
            <TableCell className="text-right">
              <Typography variant="text-s">
                {formatDate(insuranceData?.signedAt)}
              </Typography>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>
              <Typography variant="text-s">
                {' '}
                {t(LOCIZE_INSURANCE_KEYS.activeInsuranceMonthlySupport)}
              </Typography>
            </TableCell>
            <TableCell className="text-right">
              <Typography variant="text-s">
                {insuranceData?.insuredAmount} €
              </Typography>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>
              <Typography variant="text-s">
                {t(LOCIZE_INSURANCE_KEYS.activeInsuranceMonthlyCost)}
              </Typography>
            </TableCell>
            <TableCell className="text-right">
              <Typography variant="text-s">
                {insuranceData?.totalMonthlyPremiumAmount} €
              </Typography>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};

export default InsuranceDetailsTable;
