import { Typography } from '@components/typography';
import { Skeleton } from '@components/ui/skeleton';
import { Table, TableBody, TableCell, TableRow } from '@components/ui/table';
import { LOCIZE_INSURANCE_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useTranslation } from 'react-i18next';

export const InsuranceDetailsTableSkeleton = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.insurance);

  return (
    <div className="flex flex-col gap-4 mt-10">
      <Typography variant="xxs" affects="semibold">
        {t(LOCIZE_INSURANCE_KEYS.activeInsuranceDetails)}
      </Typography>
      <Table>
        <TableBody>
          <TableRow>
            <TableCell className="w-full">
              <Typography variant="text-s">
                {t(LOCIZE_INSURANCE_KEYS.activeInsuranceSigned)}
              </Typography>
            </TableCell>
            <TableCell>
              <Skeleton className="h-5 w-24" />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>
              <Typography variant="text-s">
                {t(LOCIZE_INSURANCE_KEYS.activeInsuranceMonthlySupport)}
              </Typography>
            </TableCell>
            <TableCell>
              <Skeleton className="h-5 w-24" />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>
              <Typography variant="text-s">
                {t(LOCIZE_INSURANCE_KEYS.activeInsuranceMonthlyCost)}
              </Typography>
            </TableCell>
            <TableCell>
              <Skeleton className="h-5 w-24" />
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};
