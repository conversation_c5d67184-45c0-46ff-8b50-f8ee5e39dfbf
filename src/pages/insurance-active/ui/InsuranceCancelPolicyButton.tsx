import { Button } from '@components/ui/button';
import { LOCIZE_INSURANCE_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { InsuranceStatuses } from '@entities/insurance/config';
import { useGetInsurance } from '@entities/insurance/hooks/useGetInsurance';
import { getRouteApi } from '@tanstack/react-router';
import { useTranslation } from 'react-i18next';

const routeApi = getRouteApi('/_protected/_main/insurance/active');

export const InsuranceCancelPolicyButton = () => {
  const { data: insuranceData } = useGetInsurance();
  const { t } = useTranslation(LOCIZE_NAMESPACES.insurance);
  const navigate = routeApi.useNavigate();

  const onCancelPolicy = () => {
    navigate({
      to: ROUTE_NAMES.insuranceCancellation,
      search: { fromPathname: ROUTE_NAMES.insurance, isCancellation: true },
    });
  };

  return (
    <>
      {insuranceData?.status === InsuranceStatuses.ACTIVE ? (
        <Button onClick={onCancelPolicy} variant="grey" className="w-fit mt-10">
          {t(LOCIZE_INSURANCE_KEYS.activeInsuranceCancelPolicyButton)}
        </Button>
      ) : null}
    </>
  );
};
