import { Helmet } from '@components/Helmet';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_INSURANCE_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { InsuranceInfo } from '@features/insurance-info';
import { ToggleGroup, ToggleGroupItem } from '@radix-ui/react-toggle-group';
import { useState } from 'react';
import { lazy, Suspense } from 'react';
import { useTranslation } from 'react-i18next';

import { InsuranceDetailsTableSkeleton } from './InsuranceDetailsTableSkeleton';

const InsuranceActiveCardInfo = lazy(() =>
  import('./InsuranceActiveCardInfo').then((module) => ({
    default: module.InsuranceActiveCardInfo,
  })),
);

const InsuranceDetailsTable = lazy(() =>
  import('./InsuranceDetailsTable').then((module) => ({
    default: module.InsuranceDetailsTable,
  })),
);

const InsuranceDocumentsTable = lazy(() =>
  import('./InsuranceDocumentsTable').then((module) => ({
    default: module.InsuranceDocumentsTable,
  })),
);

const InsuranceCancelPolicyButton = lazy(() =>
  import('./InsuranceCancelPolicyButton').then((module) => ({
    default: module.InsuranceCancelPolicyButton,
  })),
);

enum InsurancePageSubscribedView {
  INFO = 'INFO',
  CURRENT_INSURANCE = 'CURRENT_INSURANCE',
}

export const InsurancePageActive = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.insurance);

  const [activeTab, setActiveTab] = useState<string>(
    InsurancePageSubscribedView.CURRENT_INSURANCE,
  );

  const onValueChange = (value: string) => {
    if (value) {
      setActiveTab(value);
    }
  };

  return (
    <div className="flex flex-col px-6 pt-6 pb-10 md:px-12 md:pt-12 md:pb-20">
      <Helmet title={t(LOCIZE_INSURANCE_KEYS.insurancePageTitle)} />

      <InsuranceActiveCardInfo />

      <ToggleGroup
        className="w-fit gap-2 rounded-full bg-neutral-100 p-2"
        value={activeTab}
        defaultValue={InsurancePageSubscribedView.CURRENT_INSURANCE}
        onValueChange={onValueChange}
        type="single"
      >
        {[
          {
            label: t(LOCIZE_INSURANCE_KEYS.toggleYouInsurance),
            value: InsurancePageSubscribedView.CURRENT_INSURANCE,
          },
          {
            label: t(LOCIZE_INSURANCE_KEYS.toggleInformation),
            value: InsurancePageSubscribedView.INFO,
          },
        ].map(({ label, value }) => (
          <ToggleGroupItem
            className="aria-[checked=true]:!bg-primary-white aria-[checked=true]:!text-primary-black h-10 rounded-full px-5 py-1"
            key={value}
            value={value}
          >
            {t(label)}
          </ToggleGroupItem>
        ))}
      </ToggleGroup>

      {activeTab === InsurancePageSubscribedView.CURRENT_INSURANCE ? (
        <>
          <Suspense fallback={<InsuranceDetailsTableSkeleton />}>
            <InsuranceDetailsTable />
          </Suspense>

          <InsuranceDocumentsTable />

          <Suspense
            fallback={<Skeleton className={'mt-10 h-12 w-24 rounded-3xl'} />}
          >
            <InsuranceCancelPolicyButton />
          </Suspense>
        </>
      ) : (
        <InsuranceInfo />
      )}
    </div>
  );
};

export default InsurancePageActive;
