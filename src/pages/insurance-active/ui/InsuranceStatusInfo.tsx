import { Typography } from '@components/typography';
import { LOCIZE_INSURANCE_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useGetInsurance } from '@entities/insurance/hooks/useGetInsurance';
import ClockIcon from '@icons/clock.svg?react';
import ShieldGreenIcon from '@icons/shield-green.svg?react';
import { formatDate } from '@utils/formatters';
import { useTranslation } from 'react-i18next';

import InsuranceStatusInfoBox from './InsuranceStatusInfoBox';

export const InsuranceStatusInfo = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.insurance);

  const { data: insuranceData } = useGetInsurance();

  return (
    <div className="flex flex-col gap-1">
      <InsuranceStatusInfoBox
        status={insuranceData?.status}
        lastActiveDate={insuranceData?.lastActiveDate}
      />

      <div className="flex flex-row items-center gap-2 bg-neutral-50 rounded-2xl p-4 md:flex-row">
        <div className="flex w-10 h-10 shrink-0 rounded-full bg-white items-center justify-center">
          {insuranceData?.isTemporaryDisabilityActive ? (
            <ShieldGreenIcon className="w-6 h-6" />
          ) : (
            <ClockIcon className="w-6 h-6" />
          )}
        </div>

        <div className="flex flex-col justify-between w-full lg:flex-row">
          <Typography variant="xxs" affects="semibold">
            {t(LOCIZE_INSURANCE_KEYS.insuranceWorkAbilityTitle)}
          </Typography>
          {insuranceData?.isTemporaryDisabilityActive ? (
            <Typography className={'text-neutral-500'} variant="text-s">
              {t(LOCIZE_INSURANCE_KEYS.activeInsuranceStatusActive)}
            </Typography>
          ) : (
            <Typography className={'text-neutral-500'} variant="text-s">
              {t(LOCIZE_INSURANCE_KEYS.activeInsuranceStatusWaitingPeriod, {
                date: formatDate(
                  insuranceData?.temporaryDisabilityWaitingPeriodEndDate,
                ),
                interpolation: { escapeValue: false },
              })}
            </Typography>
          )}
        </div>
      </div>
      <div className="flex flex-row items-center gap-2 bg-neutral-50 rounded-2xl p-4 md:flex-row">
        <div className="flex w-10 h-10 shrink-0 rounded-full bg-white items-center justify-center">
          {insuranceData?.isJobLossActive ? (
            <ShieldGreenIcon className="w-6 h-6" />
          ) : (
            <ClockIcon className="w-6 h-6" />
          )}
        </div>

        <div className="flex flex-col justify-between w-full lg:flex-row">
          <Typography variant="xxs" affects="semibold">
            {t(LOCIZE_INSURANCE_KEYS.insuranceJobLossTitle)}
          </Typography>

          {insuranceData?.isJobLossActive ? (
            <Typography className={'text-neutral-500'} variant="text-s">
              {t(LOCIZE_INSURANCE_KEYS.activeInsuranceStatusActive)}
            </Typography>
          ) : (
            <Typography className={'text-neutral-500'} variant="text-s">
              {t(LOCIZE_INSURANCE_KEYS.activeInsuranceStatusWaitingPeriod, {
                date: formatDate(insuranceData?.jobLossWaitingPeriodEndDate),
                interpolation: { escapeValue: false },
              })}
            </Typography>
          )}
        </div>
      </div>
    </div>
  );
};
