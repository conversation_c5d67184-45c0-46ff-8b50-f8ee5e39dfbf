import { Typography } from '@components/typography';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_GRACE_PERIOD_KEYS } from '@config/locize/grace-period';
import { ROUTE_NAMES } from '@config/routes';
import { generateGracePeriodAgreementUrl } from '@entities/grace-period/utils';
import { useUserId } from '@entities/user';
import { useAppConfig } from '@hooks/system';
import { gracePeriodSuccessPageApi } from '@pages/grace-period-success/api';
import { getRouteApi } from '@tanstack/react-router';
import { formatDate } from '@utils/formatters';
import { SuccessLayout } from '@widgets/layouts/success';
import { useTranslation } from 'react-i18next';

const routeApi = getRouteApi('/_protected/grace-period/success');

export const GracePeriodSuccessPage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.gracePeriod);

  const navigate = routeApi.useNavigate();
  const { termsUrl } = useAppConfig();
  const { data: userId } = useUserId();
  const { data: gracePeriodEndDate } =
    gracePeriodSuccessPageApi.useSuspenseGracePeriodQuery(undefined, {
      select: (data) => {
        if (!data?.me?.active_payment_leave?.end_date)
          throw new Error('Grace period end date is missing');

        return data.me.active_payment_leave.end_date;
      },
    });

  return (
    <SuccessLayout
      title={t(LOCIZE_GRACE_PERIOD_KEYS.successTitle)}
      description={t(LOCIZE_GRACE_PERIOD_KEYS.successDescription, {
        endAt: formatDate(gracePeriodEndDate),
      })}
      before={
        <Typography className="[&_a]:underline">
          <a
            href={generateGracePeriodAgreementUrl(userId, true)}
            target="_blank"
            rel="noreferrer"
          >
            {t(LOCIZE_GRACE_PERIOD_KEYS.agreementLink)}
          </a>
          ,{' '}
          <a href={termsUrl} target="_blank" rel="noreferrer">
            {t(LOCIZE_GRACE_PERIOD_KEYS.conditionsLink)}
          </a>
        </Typography>
      }
      onClose={() => {
        navigate({
          to: ROUTE_NAMES.invoices,
        });
      }}
    />
  );
};
