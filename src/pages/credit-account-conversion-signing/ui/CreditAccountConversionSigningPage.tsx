import { ActionCard } from '@components/actionCard/ActionCard';
import { Helmet } from '@components/Helmet';
import { Button } from '@components/ui/button';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS } from '@config/locize/credit-account-conversion';
import { ROUTE_NAMES } from '@config/routes';
import { CreditAccountConversionSigningButton } from '@features/credit-account-conversion/ui/CreditAccountConversionSigningButton';
import CheckRoundedIcon from '@icons/check-rounded.svg?react';
import { getRouteApi, Link } from '@tanstack/react-router';
import { useTranslation } from 'react-i18next';

const routeApi = getRouteApi('/_protected/credit-account-conversion/signing');

export const CreditAccountConversionSigningPage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditAccountConversion);

  const { fromPathname = ROUTE_NAMES.dashboard } = routeApi.useSearch();

  return (
    <div>
      <Helmet
        title={t(LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.signingPageTitle)}
      />
      <ActionCard
        icon={<CheckRoundedIcon />}
        title={t(LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.signingTitle)}
        description={t(
          LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.signingDescription,
        )}
        before={
          <>
            <CreditAccountConversionSigningButton className="gap-12 -mb-2" />
            <Button asChild variant="white">
              <Link replace to={fromPathname}>
                {t(LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.cancel)}
              </Link>
            </Button>
          </>
        }
      />
    </div>
  );
};
