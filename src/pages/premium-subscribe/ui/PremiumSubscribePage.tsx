import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import {
  LOCIZE_ERROR_KEYS,
  LOCIZE_NAMESPACES,
  LOCIZE_PREMIUM_PAGE_KEYS,
} from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import {
  premiumSubscriptionApi,
  usePremiumSubscriptionPrice,
} from '@entities/premium';
import { userApi, useUserId } from '@entities/user';
import { useAppConfig } from '@hooks/system/useAppConfig';
import PremiumIcon from '@icons/crown.svg?react';
import { useQueryClient } from '@tanstack/react-query';
import { getRouteApi } from '@tanstack/react-router';
import { useCallback } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { toast } from 'sonner';

const routerApi = getRouteApi('/_protected/_main/premium/subscribe');

export const PremiumSubscribePage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.premiumPage);
  const { t: tErr } = useTranslation(LOCIZE_NAMESPACES.errors);

  const { data: userId } = useUserId();

  const navigate = routerApi.useNavigate();
  const queryClient = useQueryClient();
  const { premiumTermsUrl } = useAppConfig();
  const { data: premiumSubscriptionPrice } = usePremiumSubscriptionPrice();

  const subscribeToPremiumMutation =
    premiumSubscriptionApi.useSubscribeToPremiumMutation({
      onSuccess: async () => {
        await queryClient.invalidateQueries({
          queryKey: userApi.useSuspenseUserPremiumSubscriptionQuery.getKey(),
        });

        setTimeout(() => {
          navigate({
            to: ROUTE_NAMES.premium,
          });
        }, 1500);
      },
      onError: () => {
        toast.error(tErr(LOCIZE_ERROR_KEYS.generalError));
      },
    });

  const handleSubscribeButtonClick = useCallback(() => {
    subscribeToPremiumMutation.mutate({
      ownerId: userId,
    });
  }, [userId, subscribeToPremiumMutation.mutate]);

  if (subscribeToPremiumMutation.data?.create_feature_subscription) {
    return (
      <div className="flex flex-col items-center gap-6">
        <div className="size-[3.75rem] rounded-full bg-neutral-50 flex justify-center items-center text-primary-brand02">
          <PremiumIcon />
        </div>
        <Typography variant="m" tag="h3" className="mb-4">
          {t(LOCIZE_PREMIUM_PAGE_KEYS.modalSubscribeSuccessTitle)}
        </Typography>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center text-center gap-4">
      <Typography variant="m" tag="h3" className="mb-4">
        {t(LOCIZE_PREMIUM_PAGE_KEYS.modalSubscribeTitle)}
      </Typography>
      <Typography tag="p" affects="bold">
        {t(LOCIZE_PREMIUM_PAGE_KEYS.modalSubscribeDescription, {
          price: `${premiumSubscriptionPrice}€`,
        })}
      </Typography>
      <Typography variant="text-l">
        <Trans
          components={{
            termsLink: (
              <a
                href={premiumTermsUrl}
                className="underline"
                target="_blank"
                rel="noreferrer"
              />
            ),
          }}
          i18nKey={LOCIZE_PREMIUM_PAGE_KEYS.modalSubscribeSubDescription}
          t={t}
        />
      </Typography>
      <div className="mt-24 grid gap-4 w-full">
        <Button
          fullWidth
          loading={subscribeToPremiumMutation.isPending}
          onClick={handleSubscribeButtonClick}
        >
          {t(LOCIZE_PREMIUM_PAGE_KEYS.modalSubscribeButton)}
        </Button>
      </div>
    </div>
  );
};
