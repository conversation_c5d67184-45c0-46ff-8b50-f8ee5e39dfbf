import { ActionCard } from '@components/actionCard/ActionCard';
import { Button } from '@components/ui/button';
import {
  LOCIZE_NAMESPACES,
  LOCIZE_STANDING_PAYMENT_KEYS,
} from '@config/locize';
import { useStandingPaymentEnable } from '@features/standing-payment-enable/hooks';
import { useTranslation } from 'react-i18next';

export const StandingPaymentPage = () => {
  const { enableStandingPayment, isPending } = useStandingPaymentEnable();

  const { t } = useTranslation(LOCIZE_NAMESPACES.standingPayment);

  const onCtaButtonClick = () => {
    enableStandingPayment();
  };

  return (
    <ActionCard
      title={t(LOCIZE_STANDING_PAYMENT_KEYS.standingPaymentTitle)}
      description={t(LOCIZE_STANDING_PAYMENT_KEYS.standingPaymentDescription)}
      after={
        <Button
          disabled={isPending}
          onClick={onCtaButtonClick}
          className="w-full"
        >
          {t(LOCIZE_STANDING_PAYMENT_KEYS.continue)}
        </Button>
      }
    />
  );
};
