import { Typography } from '@components/typography';
import { Separator } from '@components/ui/separator';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useGetDeals } from '@entities/deals/hooks/useGetDeals';
import { useIsMobileView } from '@hooks/system';
import { DealCard } from '@pages/deals/ui/DealCard';
import { useTranslation } from 'react-i18next';

import { CardsCarousel } from '../../../shared/components/ui/CardsCarousel';
import type { DealCategoryName } from '../types';

type RelatedDealsProps = {
  category?: DealCategoryName;
  dealId: number;
};

const RelatedDeals = ({ category, dealId }: RelatedDealsProps) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);
  const isMobileView = useIsMobileView();

  if (!category) {
    return null;
  }

  const { data: deals } = useGetDeals({
    categories: category,
  });

  const relatedDeals = deals?.filter((deal) => deal.id !== dealId);

  if (!relatedDeals?.length) {
    return null;
  }

  return (
    <div className="md:px-12">
      <Separator orientation="horizontal" decorative className="mb-6" />
      <CardsCarousel
        cards={relatedDeals}
        title={
          <Typography
            tag="h2"
            variant={isMobileView ? 'xxs' : 'xs'}
            className={'flex items-center gap-3 text-left'}
          >
            {t(LOCIZE_DEALS_KEYS.relatedDeals)}
          </Typography>
        }
        renderCard={(deal) => <DealCard deal={deal} />}
      />
    </div>
  );
};

export default RelatedDeals;
