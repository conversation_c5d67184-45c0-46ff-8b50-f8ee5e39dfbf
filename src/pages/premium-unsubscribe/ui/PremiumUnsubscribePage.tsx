import { Typography } from '@components/typography';
import { But<PERSON> } from '@components/ui/button';
import { Checkbox } from '@components/ui/checkbox';
import { Form, FormField } from '@components/ui/form';
import { Textarea } from '@components/ui/textarea';
import {
  LOCIZE_ERROR_KEYS,
  LOCIZE_NAMESPACES,
  LOCIZE_PREMIUM_PAGE_KEYS,
} from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { useRudderStackAnalytics } from '@entities/analytics';
import { premiumSubscriptionApi } from '@entities/premium';
import { userApi, useUserId } from '@entities/user';
import { zodResolver } from '@hookform/resolvers/zod';
import PremiumIcon from '@icons/crown.svg?react';
import {
  PremiumUnsubscribeFormSchema,
  type PremiumUnsubscribeFormType,
  RUDDERSTACK_UNSUBSCRIBE_EVENT_NAME,
  RUDDERSTACK_UNSUBSCRIBE_EVENT_REASONS,
} from '@pages/premium-unsubscribe/config';
import { PremiumUnsubscribeReasons } from '@pages/premium-unsubscribe/types';
import { useQueryClient } from '@tanstack/react-query';
import { getRouteApi } from '@tanstack/react-router';
import { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

const routerApi = getRouteApi('/_protected/_main/premium/unsubscribe');

export const PremiumUnsubscribePage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.premiumPage);
  const { t: tErr } = useTranslation(LOCIZE_NAMESPACES.errors);

  const { data: userId } = useUserId();
  const queryClient = useQueryClient();
  const rudderStackAnalytics = useRudderStackAnalytics();
  const navigate = routerApi.useNavigate();
  const form = useForm<PremiumUnsubscribeFormType>({
    resolver: zodResolver(PremiumUnsubscribeFormSchema),
    defaultValues: {
      otherReason: '',
    },
  });

  const selectedReason = form.watch('reason');

  const handlePageClose = useCallback(() => {
    navigate({
      to: ROUTE_NAMES.premium,
    });
  }, [navigate]);

  const unsubscribeFromPremiumMutation =
    premiumSubscriptionApi.useUnsubscribeFromPremiumMutation({
      onSuccess: async () => {
        await queryClient.invalidateQueries({
          queryKey: userApi.useSuspenseUserPremiumSubscriptionQuery.getKey(),
        });

        setTimeout(() => {
          handlePageClose();
        }, 1500);
      },
      onError: () => {
        toast.error(tErr(LOCIZE_ERROR_KEYS.generalError));
      },
    });

  const handleFormSubmit = useCallback(
    async ({ otherReason, reason }: PremiumUnsubscribeFormType) => {
      await unsubscribeFromPremiumMutation.mutateAsync({
        ownerId: userId,
      });

      const cancellationReason =
        reason !== PremiumUnsubscribeReasons.OTHER_REASON
          ? RUDDERSTACK_UNSUBSCRIBE_EVENT_REASONS[reason]
          : otherReason || RUDDERSTACK_UNSUBSCRIBE_EVENT_REASONS[reason];

      rudderStackAnalytics.trackEvent({
        event: RUDDERSTACK_UNSUBSCRIBE_EVENT_NAME,
        properties: { cancellationReason },
      });
    },
    [unsubscribeFromPremiumMutation, rudderStackAnalytics.trackEvent, userId],
  );

  if (
    unsubscribeFromPremiumMutation.data?.unsubscribe_from_feature_subscription
  ) {
    return (
      <div className="flex flex-col items-center gap-6">
        <div className="size-[3.75rem] rounded-full bg-neutral-50 flex justify-center items-center text-primary-brand02">
          <PremiumIcon />
        </div>
        <Typography variant="m" tag="h3" className="mb-8 text-center">
          {t(LOCIZE_PREMIUM_PAGE_KEYS.modalUnsubscribeSuccessTitle)}
        </Typography>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form
        className="flex flex-col items-center"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        <Typography variant="m" tag="h3" className="mb-8 text-center">
          {t(LOCIZE_PREMIUM_PAGE_KEYS.modalUnsubscribeTitle)}
        </Typography>
        <Typography
          tag="p"
          variant="text-l"
          affects="semibold"
          className="mb-12 text-center"
        >
          {t(LOCIZE_PREMIUM_PAGE_KEYS.modalUnsubscribeDescription)}
        </Typography>
        <div className="mb-20 size-full overflow-hidden rounded-[1rem] border border-neutral-200">
          <div className="bg-neutral-50 p-4">
            <Typography className="text-center" affects="semibold">
              {t(LOCIZE_PREMIUM_PAGE_KEYS.unsubscribeReasonsHeading)}
            </Typography>
          </div>
          <div className="grid gap-4 p-6">
            <ul className="grid gap-4">
              {Object.values(PremiumUnsubscribeReasons).map((reason) => (
                <li key={reason} className="flex items-center gap-2">
                  <Checkbox
                    disabled={form.formState.isSubmitting}
                    checked={selectedReason === reason}
                    onCheckedChange={() => {
                      form.setValue('reason', reason);

                      if (reason !== PremiumUnsubscribeReasons.OTHER_REASON) {
                        form.setValue('otherReason', '');
                      }
                    }}
                    id={reason}
                    className="shrink-0"
                  />
                  <label htmlFor={reason} className="flex-1">
                    <Typography variant="text-s">{t(reason)}</Typography>
                  </label>
                </li>
              ))}
            </ul>
            {selectedReason === PremiumUnsubscribeReasons.OTHER_REASON && (
              <FormField
                control={form.control}
                name="otherReason"
                render={({ field }) => (
                  <Textarea
                    disabled={form.formState.isSubmitting}
                    className="resize-none"
                    {...field}
                  />
                )}
              />
            )}
          </div>
        </div>

        <div className="grid gap-4 w-full">
          <Button
            fullWidth
            disabled={form.formState.isSubmitting}
            onClick={handlePageClose}
          >
            {t(LOCIZE_PREMIUM_PAGE_KEYS.modalUnsubscribeCancelButton)}
          </Button>
          <Button
            fullWidth
            loading={form.formState.isSubmitting}
            disabled={!selectedReason}
            variant="white"
            type="submit"
          >
            {t(LOCIZE_PREMIUM_PAGE_KEYS.modalUnsubscribeConfirmButton)}
          </Button>
        </div>
      </form>
    </Form>
  );
};
