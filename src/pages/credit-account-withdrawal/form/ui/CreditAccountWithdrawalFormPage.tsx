import { useUserCreditAccount } from '@entities/user';
import {
  type CreditAccountWithdrawalFormType,
  InstantPaymentStatus,
} from '@features/credit-account/withdrawal-form';
import { creditAccountWithdrawalApi } from '@pages/credit-account-withdrawal/form/api';
import { lazy, useCallback, useMemo, useState } from 'react';

import { useCawPricing, useWithdrawFromCreditAccount } from '../hooks';

const CreditAccountWithdrawalForm = lazy(() =>
  import('@features/credit-account/withdrawal-form').then((m) => ({
    default: m.CreditAccountWithdrawalForm,
  })),
);

const CreditAccountWithdrawalFormConfirmView = lazy(() =>
  import('./CreditAccountWithdrawalFormConfirmView').then((m) => ({
    default: m.CreditAccountWithdrawalFormConfirmView,
  })),
);

const CreditAccountWithdrawalFormPendingView = lazy(() =>
  import('./CreditAccountWithdrawalFormPendingView').then((m) => ({
    default: m.CreditAccountWithdrawalFormPendingView,
  })),
);

export const CreditAccountWithdrawalFormPage = () => {
  const [withdrawData, setWithdrawData] =
    useState<Nullable<CreditAccountWithdrawalFormType>>(null);
  const [isConfirming, setIsConfirming] = useState(false);

  const { data: creditAccount } = useUserCreditAccount();
  if (!creditAccount) throw new Error('Credit account is not found');
  const { data: pricing } = useCawPricing();
  const { data: cawSettingsData } =
    creditAccountWithdrawalApi.useSuspenseCreditAccountWithdrawalSettingsQuery({
      creditAccountId: creditAccount.id,
      amount: pricing.minInstantWithdrawalAmount,
    });

  const { handleWithdrawFromCredit, isWithdrawing, isWithdrawRequested } =
    useWithdrawFromCreditAccount();

  const instantPaymentStatus = useMemo(() => {
    const { instant_payment_enabled, instant_payment_fee } =
      cawSettingsData?.credit_account_withdrawal ?? {};

    if (!instant_payment_enabled) {
      return InstantPaymentStatus.DISABLED;
    }

    if (instant_payment_fee === 0) {
      return InstantPaymentStatus.ENABLED_BY_DEFAULT;
    }

    return InstantPaymentStatus.ENABLED;
  }, [cawSettingsData?.credit_account_withdrawal]);

  const handleFormSubmit = useCallback(
    ({ amount, isInstantPayment }: CreditAccountWithdrawalFormType) => {
      setWithdrawData({ amount, isInstantPayment });
      setIsConfirming(true);
    },
    [],
  );

  const handleWithdrawConfirm = useCallback(() => {
    if (!withdrawData) {
      throw new Error('Withdraw data is missing');
    }

    handleWithdrawFromCredit({
      amount: withdrawData.amount,
      isInstantPayment: withdrawData.isInstantPayment,
      creditAccountId: creditAccount.id,
    });
  }, [creditAccount.id, handleWithdrawFromCredit, withdrawData]);

  if (isWithdrawing || isWithdrawRequested) {
    return (
      <CreditAccountWithdrawalFormPendingView
        isWithdrawRequested={isWithdrawRequested}
      />
    );
  }

  if (isConfirming && withdrawData) {
    return (
      <CreditAccountWithdrawalFormConfirmView
        amount={withdrawData.amount}
        onSubmit={handleWithdrawConfirm}
        onClose={() => setIsConfirming(false)}
      />
    );
  }

  return (
    <CreditAccountWithdrawalForm
      creditAccountId={creditAccount.id}
      instantPaymentFee={
        cawSettingsData.credit_account_withdrawal?.instant_payment_fee ?? 0
      }
      maxWithdrawalAmount={
        Math.floor(
          (creditAccount.creditLimit - creditAccount.unpaidPrincipal) /
            pricing.minWithdrawalAmount,
        ) * pricing.minWithdrawalAmount
      }
      minInstantWithdrawalAmount={pricing.minInstantWithdrawalAmount}
      minWithdrawalAmount={pricing.minWithdrawalAmount}
      onSubmit={handleFormSubmit}
      defaultValues={withdrawData}
      instantPaymentStatus={instantPaymentStatus}
    />
  );
};
