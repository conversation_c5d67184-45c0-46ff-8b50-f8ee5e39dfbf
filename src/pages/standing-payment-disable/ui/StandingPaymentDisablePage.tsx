import { ActionCard } from '@components/actionCard/ActionCard';
import { Button } from '@components/ui/button';
import {
  LOCIZE_NAMESPACES,
  LOCIZE_STANDING_PAYMENT_KEYS,
} from '@config/locize';
import { useStandingPaymentDisable } from '@features/standing-payment-disable/hooks';
import { useTranslation } from 'react-i18next';

export const StandingPaymentDisablePage = () => {
  const { disableStandingPayment, isPending } = useStandingPaymentDisable();

  const { t } = useTranslation(LOCIZE_NAMESPACES.standingPayment);

  const onCtaButtonClick = () => {
    disableStandingPayment();
  };

  return (
    <ActionCard
      title={t(LOCIZE_STANDING_PAYMENT_KEYS.disableTitle)}
      description={t(LOCIZE_STANDING_PAYMENT_KEYS.disableDescription)}
      after={
        <Button
          disabled={isPending}
          onClick={onCtaButtonClick}
          className="transition-colors w-full hover:text-gray-100 bg-gray-100 text-black"
        >
          {t(LOCIZE_STANDING_PAYMENT_KEYS.disableCancelStandingPayment)}
        </Button>
      }
    />
  );
};
