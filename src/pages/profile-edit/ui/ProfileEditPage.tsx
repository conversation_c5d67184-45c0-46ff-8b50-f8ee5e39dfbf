import { CheckboxWrapper } from '@components/CheckboxWrapper';
import { PhoneInput } from '@components/PhoneInput';
import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@components/ui/form';
import { Input } from '@components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@components/ui/select';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_PROFILE_PAGE_KEYS } from '@config/locize/profile-page';
import { EMPLOYMENT_DATE_OPTIONS } from '@config/profile/employment-date';
import { UserProfileFormFields } from '@config/profile/form';
import { occupationCategoriesApi } from '@entities/occupation-categories/api';
import { politicalExposuresApi } from '@entities/political-exposures/api';
import { useIsCountry } from '@hooks/system';
import { useTranslation } from 'react-i18next';
import { NumericFormat } from 'react-number-format';

import {
  OccupationCategory,
  PoliticalExposure,
  SupportedCountries,
} from '@/shared/types';

import { useEditProfileForm } from '../hooks/useEditProfileForm';

export const ProfileEditPage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.profilePage);
  const isLtRegion = useIsCountry(SupportedCountries.LT);
  const {
    data: { political_exposures: politicalExposures },
  } = politicalExposuresApi.useSuspensePoliticalExposuresQuery();
  const {
    data: { occupation_categories: occupationCategories },
  } = occupationCategoriesApi.useSuspenseOccupationCategoriesQuery();

  const {
    form,
    handleSubmit,
    planningNewDebts,
    futureReducedEarnings,
    overdueDebt,
    userFullName,
    isVisiblePersonalField,
    isVisibleFinancialField,
    isFetching,
  } = useEditProfileForm();

  if (isFetching) {
    return <Skeleton className="h-[90rem] w-full md:rounded-3xl" />;
  }

  return (
    <div>
      <Typography className="order-2 lg:order-1" variant="s">
        {userFullName}
      </Typography>
      <Typography className="mt-10" variant="xs">
        {t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.personalInfoTitle)}
      </Typography>
      <Form {...form}>
        <form
          className="mx-auto grid gap-2 w-full mt-6"
          onSubmit={form.handleSubmit(handleSubmit)}
        >
          {isVisiblePersonalField(UserProfileFormFields.EMAIL) ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.EMAIL}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.emailLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <Input disabled={form.formState.isSubmitting} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {isVisiblePersonalField(UserProfileFormFields.PHONE) ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.PHONE}
              render={({ field: { onChange, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.phoneLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <PhoneInput
                      disabled={form.formState.isSubmitting}
                      onValueChange={({ value }) => {
                        onChange(value);
                      }}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}
          {isVisiblePersonalField(UserProfileFormFields.ADDRESS) ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.ADDRESS}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .addressLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <Input disabled={form.formState.isSubmitting} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {isVisiblePersonalField(UserProfileFormFields.POST_CODE) ||
          isVisiblePersonalField(UserProfileFormFields.CITY) ? (
            <div className="grid w-full grid-cols-[repeat(auto-fit,minmax(4rem,1fr))] gap-6">
              {isVisiblePersonalField(UserProfileFormFields.CITY) ? (
                <FormField
                  control={form.control}
                  name={UserProfileFormFields.CITY}
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>
                        {t(
                          LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                            .cityLabel,
                        )}
                      </FormLabel>
                      <FormControl>
                        <Input
                          disabled={form.formState.isSubmitting}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ) : null}

              {isVisiblePersonalField(UserProfileFormFields.POST_CODE) ? (
                <FormField
                  control={form.control}
                  name={UserProfileFormFields.POST_CODE}
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>
                        {t(
                          LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                            .postCodeLabel,
                        )}
                      </FormLabel>
                      <FormControl>
                        <Input
                          disabled={form.formState.isSubmitting}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ) : null}
            </div>
          ) : null}

          {isVisiblePersonalField(UserProfileFormFields.POLITICAL_EXPOSURE) ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.POLITICAL_EXPOSURE}
              render={({ field: { onChange, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .politicalExposureLabel,
                    )}
                  </FormLabel>
                  <Select
                    disabled={form.formState.isSubmitting}
                    onValueChange={onChange}
                    value={field.value}
                    info={t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .politicalExposureTooltip,
                    )}
                  >
                    <FormControl>
                      <SelectTrigger className="[&>span]:w-1 [&>span]:flex-1 [&>span]:truncate">
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {politicalExposures?.map((politicalExposure) => {
                        if (!politicalExposure) return null;

                        if (!(politicalExposure in PoliticalExposure))
                          return null;

                        return (
                          <SelectItem
                            key={politicalExposure}
                            value={politicalExposure}
                          >
                            {t(
                              // @ts-expect-error need to fix
                              LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                                .politicalExposure[politicalExposure],
                            )}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          <Typography className="mt-12 mb-6" variant="xs">
            {t(
              LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.financialInfoTitle,
            )}
          </Typography>

          {isVisibleFinancialField(
            UserProfileFormFields.OCCUPATION_CATEGORY,
          ) ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.OCCUPATION_CATEGORY}
              render={({ field: { onChange, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .occupationLabel,
                    )}
                  </FormLabel>
                  <Select
                    disabled={form.formState.isSubmitting}
                    onValueChange={onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="[&>span]:w-1 [&>span]:flex-1 [&>span]:truncate">
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {occupationCategories?.map((occupationCategory) => {
                        if (!occupationCategory) return null;

                        if (!(occupationCategory in OccupationCategory))
                          return null;

                        return (
                          <SelectItem
                            key={occupationCategory}
                            value={occupationCategory}
                          >
                            {t(
                              // @ts-expect-error need to fix
                              LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                                .occupation[occupationCategory],
                            )}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {isVisibleFinancialField(UserProfileFormFields.EMPLOYMENT_DATE) ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.EMPLOYMENT_DATE}
              render={({ field: { onChange, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .employmentDateLabel,
                    )}
                  </FormLabel>
                  <Select
                    disabled={form.formState.isSubmitting}
                    onValueChange={onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="[&>span]:w-1 [&>span]:flex-1 [&>span]:truncate">
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {EMPLOYMENT_DATE_OPTIONS.map(({ label, value }) => (
                        <SelectItem key={value} value={value}>
                          {t(label)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {isVisibleFinancialField(UserProfileFormFields.IBAN) ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.IBAN}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.ibanLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <Input
                      disabled={form.formState.isSubmitting}
                      info={t(
                        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                          .ibanTooltip,
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {isVisibleFinancialField(UserProfileFormFields.NET_INCOME_MONTHLY) ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.NET_INCOME_MONTHLY}
              render={({ field: { onChange, value, name, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      isLtRegion
                        ? LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                            .netIncomeMonthlyLabelLt
                        : LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                            .netIncomeMonthlyLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <NumericFormat
                      disabled={form.formState.isSubmitting}
                      {...field}
                      allowLeadingZeros={false}
                      allowNegative={false}
                      className="amountInput"
                      fixedDecimalScale
                      name={`${name}-input`}
                      onValueChange={(v) => {
                        onChange(v.floatValue);
                      }}
                      value={value}
                      after={
                        <Typography className="mr-2 flex items-center text-neutral-400">
                          €
                        </Typography>
                      }
                      info={t(
                        isLtRegion
                          ? LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                              .netIncomeMonthlyTooltipLt
                          : LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                              .netIncomeMonthlyTooltip,
                      )}
                      customInput={Input}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {isVisibleFinancialField(
            UserProfileFormFields.MONTHLY_LIVING_EXPENSES,
          ) ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.MONTHLY_LIVING_EXPENSES}
              render={({ field: { onChange, value, name, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .monthlyLivingExpensesLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <NumericFormat
                      {...field}
                      disabled={form.formState.isSubmitting}
                      allowLeadingZeros={false}
                      allowNegative={false}
                      className="amountInput"
                      fixedDecimalScale
                      name={`${name}-input`}
                      onValueChange={(v) => {
                        onChange(v.floatValue);
                      }}
                      value={value}
                      after={
                        <Typography className="mr-2 flex items-center text-neutral-400">
                          €
                        </Typography>
                      }
                      info={t(
                        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                          .monthlyLivingExpensesTooltip,
                      )}
                      customInput={Input}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {isVisibleFinancialField(
            UserProfileFormFields.EXPENDITURE_MONTHLY,
          ) ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.EXPENDITURE_MONTHLY}
              render={({ field: { onChange, value, name, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .expenditureMonthlyLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <NumericFormat
                      {...field}
                      allowLeadingZeros={false}
                      disabled={form.formState.isSubmitting}
                      allowNegative={false}
                      className="amountInput"
                      fixedDecimalScale
                      name={`${name}-input`}
                      onValueChange={(v) => {
                        onChange(v.floatValue);
                      }}
                      value={value}
                      after={
                        <Typography className="mr-2 flex items-center text-neutral-400">
                          €
                        </Typography>
                      }
                      info={t(
                        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                          .expenditureMonthlyLabel,
                      )}
                      customInput={Input}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {isVisibleFinancialField(UserProfileFormFields.OVERDUE_DEBT) ? (
            <CheckboxWrapper
              className="mb-4 transition-all"
              checkedClassName="mb-0"
              defaultChecked={!!overdueDebt}
              onCheckedChange={(checked) => {
                if (!checked) {
                  form.setValue(UserProfileFormFields.OVERDUE_DEBT, 0);
                }
              }}
              label={t(
                LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                  .overdueDebtCheckboxLabel,
              )}
            >
              <FormField
                control={form.control}
                name={UserProfileFormFields.OVERDUE_DEBT}
                render={({ field: { onChange, value, name, ...field } }) => {
                  return (
                    <FormItem className="w-full">
                      <FormLabel>
                        {t(
                          LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                            .overdueDebtLabel,
                        )}
                      </FormLabel>
                      <FormControl>
                        <NumericFormat
                          {...field}
                          disabled={form.formState.isSubmitting}
                          autoFocus={
                            !overdueDebt || form.formState.dirtyFields[name]
                          }
                          allowLeadingZeros={false}
                          allowNegative={false}
                          fixedDecimalScale
                          name={`${name}-input`}
                          onValueChange={(v) => {
                            onChange(v.floatValue);
                          }}
                          value={value}
                          after={
                            <Typography className="mr-2 flex items-center text-neutral-400">
                              €
                            </Typography>
                          }
                          customInput={Input}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </CheckboxWrapper>
          ) : null}

          {isVisibleFinancialField(
            UserProfileFormFields.NUMBER_OF_DEPENDENTS,
          ) ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.NUMBER_OF_DEPENDENTS}
              render={({ field: { onChange, value, name, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .numberOfDependentsLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <NumericFormat
                      {...field}
                      disabled={form.formState.isSubmitting}
                      allowLeadingZeros={false}
                      allowNegative={false}
                      className="amountInput"
                      name={`${name}-input`}
                      onValueChange={(v) => {
                        onChange(v.floatValue);
                      }}
                      value={value}
                      customInput={Input}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {isVisibleFinancialField(UserProfileFormFields.PLANNING_NEW_DEBTS) ? (
            <CheckboxWrapper
              className="mb-4 transition-all"
              checkedClassName="mb-0"
              defaultChecked={!!planningNewDebts}
              onCheckedChange={(checked) => {
                if (!checked) {
                  form.setValue(UserProfileFormFields.PLANNING_NEW_DEBTS, 0);
                }
              }}
              label={t(
                LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                  .planningNewDebtsCheckboxLabel,
              )}
            >
              <FormField
                control={form.control}
                name={UserProfileFormFields.PLANNING_NEW_DEBTS}
                render={({ field: { onChange, value, name, ...field } }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {t(
                        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                          .planningNewDebtsLabel,
                      )}
                    </FormLabel>
                    <FormControl>
                      <NumericFormat
                        {...field}
                        disabled={form.formState.isSubmitting}
                        autoFocus={
                          !planningNewDebts || form.formState.dirtyFields[name]
                        }
                        allowLeadingZeros={false}
                        allowNegative={false}
                        fixedDecimalScale
                        name={`${name}-input`}
                        onValueChange={(v) => {
                          onChange(v.floatValue);
                        }}
                        value={value}
                        after={
                          <Typography className="mr-2 flex items-center text-neutral-400">
                            €
                          </Typography>
                        }
                        customInput={Input}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CheckboxWrapper>
          ) : null}

          {isVisibleFinancialField(
            UserProfileFormFields.FUTURE_REDUCED_EARNINGS,
          ) ? (
            <CheckboxWrapper
              className="mb-4 transition-all"
              checkedClassName="mb-0"
              defaultChecked={!!futureReducedEarnings}
              onCheckedChange={(checked) => {
                if (!checked) {
                  form.setValue(
                    UserProfileFormFields.FUTURE_REDUCED_EARNINGS,
                    0,
                  );
                }
              }}
              label={t(
                LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                  .futureReducedEarningsCheckboxLabel,
              )}
            >
              <FormField
                control={form.control}
                name={UserProfileFormFields.FUTURE_REDUCED_EARNINGS}
                render={({ field: { onChange, value, name, ...field } }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {t(
                        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                          .futureReducedEarningsCheckboxLabel,
                      )}
                    </FormLabel>
                    <FormControl>
                      <NumericFormat
                        {...field}
                        disabled={form.formState.isSubmitting}
                        autoFocus={
                          !futureReducedEarnings ||
                          form.formState.dirtyFields[name]
                        }
                        allowLeadingZeros={false}
                        allowNegative={false}
                        fixedDecimalScale
                        name={`${name}-input`}
                        onValueChange={(v) => {
                          onChange(v.floatValue);
                        }}
                        value={value}
                        after={
                          <Typography className="mr-2 flex items-center text-neutral-400">
                            €
                          </Typography>
                        }
                        customInput={Input}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CheckboxWrapper>
          ) : null}

          {isVisibleFinancialField(
            UserProfileFormFields.ULTIMATE_BENEFICIAL_OWNER,
          ) ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.ULTIMATE_BENEFICIAL_OWNER}
              render={({ field: { onChange, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .ultimateBeneficialOwnerLabel,
                    )}
                  </FormLabel>
                  <Select
                    disabled={form.formState.isSubmitting}
                    info={t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .ultimateBeneficialOwnerTooltip,
                    )}
                    onValueChange={onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="[&>span]:w-1 [&>span]:flex-1 [&>span]:truncate">
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={'1'}>
                        {t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.yes)}
                      </SelectItem>
                      <SelectItem value={'0'}>
                        {t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.no)}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          <Button
            className="w-fit"
            loading={form.formState.isSubmitting}
            disabled={!form.formState.isDirty}
            type="submit"
          >
            {t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.saveButton)}
          </Button>
        </form>
      </Form>
    </div>
  );
};
