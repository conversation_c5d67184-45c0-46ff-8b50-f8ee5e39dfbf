import { Button } from '@components/ui/button';
import { LOCIZE_INVOICE_PAYMENT_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useUserRecentInvoiceByReference } from '@entities/invoices';
import { userApi } from '@entities/user';
import { InvoicePaymentFormView } from '@pages/invoice-payment/config';
import { useInvoicePaymentInfo } from '@pages/invoice-payment/hooks/useInvoicePaymentInfo';
import { useInvoicePaymentUserInfo } from '@pages/invoice-payment/hooks/useInvoicePaymentUserInfo';
import type { InvoicePaymentPremiumFormType } from '@pages/invoice-payment/schemas';
import { getRouteApi } from '@tanstack/react-router';
import { processGqlFormValidationErrors } from '@utils/parseGraphQLError';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

const routeApi = getRouteApi('/payment/invoice/');

export const InvoicePaymentControls = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.invoicePayment);

  const { data: user } = useInvoicePaymentUserInfo();
  const { data: userInvoice } = useUserRecentInvoiceByReference();
  if (!userInvoice) throw new Error('User doesnt have invoice');

  const navigate = routeApi.useNavigate();
  const { withPremium } = routeApi.useSearch();
  const { isFetching: isInvoicePaymentFetching } = useInvoicePaymentInfo();
  const form = useFormContext<InvoicePaymentPremiumFormType>();

  const updateUserMutation = userApi.useUpdateUserMutation();

  const redirectToPaymentForm = async () => {
    await navigate({
      search: (prev) => ({
        ...prev,
        formView: InvoicePaymentFormView.PAYMENT,
      }),
    });
  };

  const handleContinueButtonClick = async () => {
    if (!withPremium || !user || (withPremium && user.hasRequiredData)) {
      await redirectToPaymentForm();
      return;
    }

    form.handleSubmit(async (data) => {
      try {
        await updateUserMutation.mutateAsync({
          userId: user.id,
          email: data.email,
          phone: data.phone,
        });

        await redirectToPaymentForm();
      } catch (error) {
        processGqlFormValidationErrors({
          error,
          setFormError: form.setError,
        });
      }
    })();
  };

  return (
    <div className="grid gap-4 mt-14">
      <Button
        onClick={handleContinueButtonClick}
        disabled={isInvoicePaymentFetching}
        loading={form.formState.isSubmitting}
      >
        {t(LOCIZE_INVOICE_PAYMENT_KEYS.continue)}
      </Button>
      {userInvoice?.url ? (
        <Button variant="white" asChild>
          <a href={userInvoice.url} target="_blank" rel="noreferrer">
            {t(LOCIZE_INVOICE_PAYMENT_KEYS.downloadInvoice)}
          </a>
        </Button>
      ) : null}
    </div>
  );
};
