import { Helmet } from '@components/Helmet';
import { LateSuspense } from '@components/LateSuspense';
import { LoadingSpinner } from '@components/LoadingSpinner';
import { Typography } from '@components/typography';
import { Form } from '@components/ui/form';
import { LOCIZE_INVOICE_PAYMENT_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { useUserRecentInvoiceByReference } from '@entities/invoices';
import { AppPaymentMethod } from '@entities/payments';
import { useIsUserAuthorized } from '@entities/user';
import {
  PaymentMethodsSelect,
  type PaymentMethodsSelectProps,
} from '@features/payment-methods-select';
import { useIsMobileView } from '@hooks/system';
import { InvoicePaymentFormView } from '@pages/invoice-payment/config';
import { useInvoicePaymentInfo } from '@pages/invoice-payment/hooks/useInvoicePaymentInfo';
import { useInvoicePaymentPremiumForm } from '@pages/invoice-payment/hooks/useInvoicePaymentPremiumForm';
import { InvoicePaymentControls } from '@pages/invoice-payment/ui/invoice-payment-controls/InvoicePaymentControls';
import { InvoicePaymentInfo } from '@pages/invoice-payment/ui/invoice-payment-info';
import {
  InvoicePaymentPremiumOffer,
  InvoicePaymentPremiumOfferSkeleton,
} from '@pages/invoice-payment/ui/invoice-payment-premium-offer';
import { getRouteApi } from '@tanstack/react-router';
import { PaymentAuthHeader } from '@widgets/headers/payment-auth-header';
import { DualPanelLayout } from '@widgets/layouts/dual-panel';
import { lazy, type ReactNode, Suspense } from 'react';
import { useTranslation } from 'react-i18next';
import { useUpdateEffect } from 'react-use';

const InvoicePaymentManualTransferInfo = lazy(() =>
  import('./invoice-payment-manual-transfer-info').then(
    ({ InvoicePaymentManualTransferInfo }) => ({
      default: InvoicePaymentManualTransferInfo,
    }),
  ),
);
const BanklinkPaymentForm = lazy(() =>
  import('@widgets/banklink-payment').then(({ BanklinkPaymentForm }) => ({
    default: BanklinkPaymentForm,
  })),
);
const CreditCardPaymentSelect = lazy(() =>
  import('@features/credit-card-payment').then(
    ({ CreditCardPaymentSelect }) => ({
      default: CreditCardPaymentSelect,
    }),
  ),
);

const routeApi = getRouteApi('/payment/invoice/');

const paymentMethodRenderer: Record<
  AppPaymentMethod,
  (data: {
    userId: number;
    amount: number;
    redirectUrl: string;
    onCancel: () => void;
  }) => ReactNode
> = {
  [AppPaymentMethod.BANKLINK]: (data) => <BanklinkPaymentForm {...data} />,
  [AppPaymentMethod.CARD]: (data) => <CreditCardPaymentSelect {...data} />,
  [AppPaymentMethod.MANUAL_TRANSFER]: ({ onCancel }) => (
    <InvoicePaymentManualTransferInfo onCancel={onCancel} />
  ),
};

export const InvoicePaymentPage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.invoicePayment);
  const isMobileView = useIsMobileView();
  const { data: isAuthorized } = useIsUserAuthorized();

  return (
    <>
      <Helmet title={t(LOCIZE_INVOICE_PAYMENT_KEYS.pageTitle)} />
      <DualPanelLayout
        fromPage={ROUTE_NAMES.invoices}
        header={!isAuthorized ? <PaymentAuthHeader /> : undefined}
        left={isMobileView ? null : <InvoicePaymentInfo />}
        right={<InvoicePaymentRightPanel />}
      />
    </>
  );
};

function InvoicePaymentRightPanel() {
  const { t } = useTranslation(LOCIZE_NAMESPACES.invoicePayment);
  const { data: recentInvoiceInfo } = useUserRecentInvoiceByReference();

  if (!recentInvoiceInfo) {
    throw new Error('No recent invoice info');
  }

  const { data: payment } = useInvoicePaymentInfo();
  const isMobileView = useIsMobileView();

  const {
    formView = InvoicePaymentFormView.OVERVIEW,
    paymentMethod,
    withPremium,
  } = routeApi.useSearch();

  const navigate = routeApi.useNavigate();
  const form = useInvoicePaymentPremiumForm();

  const onPaymentMethodChange: PaymentMethodsSelectProps['onValueChange'] = (
    value,
  ) => {
    navigate({
      replace: true,
      search: (prev) => ({
        ...prev,
        paymentMethod: value,
      }),
    });
  };

  const onCancelPaymentMethod = () => {
    navigate({
      replace: true,
      search: (prev) => ({
        ...prev,
        formView: InvoicePaymentFormView.OVERVIEW,
      }),
    });
  };

  useUpdateEffect(() => {
    if (!withPremium && form.formState.isDirty) {
      form.reset();
    }
  });

  return (
    <div className="w-full max-w-[25rem] flex flex-col h-full">
      <Typography variant="s" tag="h1" className="mb-10 md:mt-6 md:mb-8">
        {t(LOCIZE_INVOICE_PAYMENT_KEYS.pageTitle)}
      </Typography>
      <div className="flex flex-col md:p-0 md:flex-1 md:justify-center md:max-h-[689px]">
        <LateSuspense fallback={<LoadingSpinner className="mx-auto" />}>
          {formView === InvoicePaymentFormView.OVERVIEW ? (
            <>
              {isMobileView ? <InvoicePaymentInfo /> : null}
              <PaymentMethodsSelect
                value={paymentMethod}
                onValueChange={onPaymentMethodChange}
              />
              <Form {...form}>
                <Suspense fallback={<InvoicePaymentPremiumOfferSkeleton />}>
                  <InvoicePaymentPremiumOffer />
                </Suspense>
                <InvoicePaymentControls />
              </Form>
            </>
          ) : payment?.card_payment_fee ? (
            paymentMethodRenderer[paymentMethod]({
              userId: recentInvoiceInfo.userId,
              amount: payment.card_payment_fee.amount,
              redirectUrl: `${window.origin}${ROUTE_NAMES.paymentInvoice}`,
              onCancel: onCancelPaymentMethod,
            })
          ) : (
            // TODO: Add loading state
            <LoadingSpinner className="mx-auto" />
          )}
        </LateSuspense>
      </div>
    </div>
  );
}
