import { PhoneInput } from '@components/PhoneInput';
import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { FormControl, FormField, FormItem } from '@components/ui/form';
import { Input } from '@components/ui/input';
import { Separator } from '@components/ui/separator';
import { Skeleton } from '@components/ui/skeleton';
import {
  LOCIZE_COMMON_KEYS,
  LOCIZE_INVOICE_PAYMENT_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { usePremiumSubscriptionPrice } from '@entities/premium';
import { userApi, useUserPremiumSubscription } from '@entities/user';
import { useAppConfig } from '@hooks/system';
import type { InvoicePaymentPremiumFormType } from '@pages/invoice-payment/schemas';
import { getRouteApi, Link } from '@tanstack/react-router';
import { useFormContext } from 'react-hook-form';
import { Trans, useTranslation } from 'react-i18next';

const routeApi = getRouteApi('/payment/invoice/');

export const InvoicePaymentPremiumOffer = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.invoicePayment);
  const form = useFormContext<InvoicePaymentPremiumFormType>();
  const { withPremium } = routeApi.useSearch();
  const { data: userPremiumSubscription } = useUserPremiumSubscription();
  const { data: premiumSubscriptionPrice } = usePremiumSubscriptionPrice();
  const { premiumTermsUrl } = useAppConfig();
  const { data: user } = userApi.useSuspenseUserQuery(undefined, {
    select: (data) => {
      if (!data?.me) {
        return null;
      }

      return {
        email: data.me.email,
        phone: data.me.phone,
        hasRequiredData: !!data.me.phone && !!data.me.email,
      };
    },
  });

  if (!user || userPremiumSubscription) return null;

  return (
    <div className="shadow-container rounded-[.875rem] border border-neutral-200 p-4 mt-8">
      <div className="flex items-center justify-between gap-[10px] mb-[18px]">
        <Typography affects="semibold">
          {t(LOCIZE_INVOICE_PAYMENT_KEYS.premiumOfferTitle)}
        </Typography>
        <Typography>{premiumSubscriptionPrice} €</Typography>
      </div>
      <Typography variant="text-s">
        {t(LOCIZE_INVOICE_PAYMENT_KEYS.premiumOfferDescription)}
      </Typography>
      <Typography variant="text-s" className="mb-6">
        <Trans
          components={{
            termsLink: (
              <a
                href={premiumTermsUrl}
                className="underline"
                target="_blank"
                rel="noreferrer"
              />
            ),
            br: <br />,
          }}
          i18nKey={LOCIZE_INVOICE_PAYMENT_KEYS.premiumOfferTerms}
          t={t}
        />
      </Typography>
      <Separator decorative />
      <div className="mt-4 flex flex-col items-start gap-4 [&>div]:w-full">
        {user && withPremium && !user.hasRequiredData ? (
          <>
            {!user?.email ? (
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        disabled={form.formState.isSubmitting}
                        placeholder={t(LOCIZE_COMMON_KEYS.emailPlaceholder)}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            ) : null}
            {!user?.phone ? (
              <FormField
                control={form.control}
                name="phone"
                render={({ field: { onChange, ...field } }) => (
                  <FormItem>
                    <FormControl>
                      <PhoneInput
                        disabled={form.formState.isSubmitting}
                        onValueChange={({ value }) => {
                          onChange(value);
                        }}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            ) : null}
          </>
        ) : null}
        {withPremium ? (
          <Button variant="grey" size="small" asChild>
            <Link
              to={ROUTE_NAMES.current}
              search={(prev) => ({ ...prev, withPremium: false })}
              replace
            >
              {t(LOCIZE_INVOICE_PAYMENT_KEYS.premiumOfferCancel)}
            </Link>
          </Button>
        ) : (
          <Button variant="blue" size="small" asChild>
            <Link
              to={ROUTE_NAMES.current}
              search={(prev) => ({ ...prev, withPremium: true })}
              replace
              resetScroll={false}
            >
              {t(LOCIZE_INVOICE_PAYMENT_KEYS.premiumOfferAddButton)}
            </Link>
          </Button>
        )}
      </div>
    </div>
  );
};

export const InvoicePaymentPremiumOfferSkeleton = () => (
  <Skeleton className="h-[209px] w-full rounded-3xl mt-8" />
);
