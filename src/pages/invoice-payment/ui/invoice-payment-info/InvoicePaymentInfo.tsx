import { Typography } from '@components/typography';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_INVOICE_PAYMENT_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useUserRecentInvoiceByReference } from '@entities/invoices';
import { formatDate, formatNumber } from '@utils/formatters';
import { useTranslation } from 'react-i18next';

import { useInvoicePaymentInfo } from '../../hooks/useInvoicePaymentInfo';

export const InvoicePaymentInfo = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.invoicePayment);

  const { data: userInvoice } = useUserRecentInvoiceByReference();

  if (!userInvoice) throw new Error('User doesnt have invoice');

  const { data: payment, isFetching: isPaymentFetching } =
    useInvoicePaymentInfo();

  return (
    <div className="space-y-12 grid w-full mb-8 md:mb-0 md:max-w-[25rem]">
      <div className="grid rounded-[.875rem] overflow-hidden border border-neutral-200 bg-primary-white">
        <div className="hidden bg-neutral-700 px-6 py-[1.125rem] md:flex items-center justify-between gap-4">
          <Typography
            affects="semibold"
            tag="span"
            className="text-neutral-400"
          >
            {t(LOCIZE_INVOICE_PAYMENT_KEYS.infoTitle)}
          </Typography>
        </div>

        <div className="grid p-4 divide-y-[1px] divide-neutral-200 [&>div:first-child]:pt-0 [&>div:last-child]:pb-0">
          <div className="flex items-center justify-between gap-2 py-3">
            <Typography variant="text-s" className="text-neutral-500">
              {t(LOCIZE_INVOICE_PAYMENT_KEYS.dueDate)}
            </Typography>
            <Typography variant="text-s">
              {formatDate(userInvoice?.nextPaymentDate)}
            </Typography>
          </div>
          <div className="flex items-center justify-between gap-2 py-3">
            <Typography variant="text-s" className="text-neutral-500">
              {t(LOCIZE_INVOICE_PAYMENT_KEYS.reference)}
            </Typography>
            <Typography variant="text-s">{userInvoice?.referenceNr}</Typography>
          </div>
          <div className="flex items-center justify-between gap-2 py-3">
            {isPaymentFetching || !payment?.card_payment_fee ? (
              <>
                <Typography variant="text-s" className="text-neutral-500">
                  {t(LOCIZE_INVOICE_PAYMENT_KEYS.total)}
                </Typography>
                <Skeleton className="h-[1.25rem] w-14" />
              </>
            ) : (
              <>
                <Typography variant="text-s" className="text-neutral-500">
                  {payment.card_payment_fee.card_payment_fee_pct
                    ? t(LOCIZE_INVOICE_PAYMENT_KEYS.totalAndFee, {
                        fee: payment.card_payment_fee.card_payment_fee_pct,
                      })
                    : t(LOCIZE_INVOICE_PAYMENT_KEYS.total)}
                </Typography>
                <Typography variant="text-s">
                  {formatNumber({
                    value: payment.card_payment_fee.card_payment_fee_pct
                      ? payment.card_payment_fee.amount_with_fee
                      : payment.card_payment_fee.amount,
                  })}{' '}
                  €
                </Typography>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
