import { CheckboxWrapper } from '@components/CheckboxWrapper';
import { PhoneInput } from '@components/PhoneInput';
import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@components/ui/form';
import { Input } from '@components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@components/ui/select';
import { Switch } from '@components/ui/switch';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_PROFILE_PAGE_KEYS } from '@config/locize/profile-page';
import { EMPLOYMENT_DATE_OPTIONS } from '@config/profile/employment-date';
import { occupationCategoriesApi } from '@entities/occupation-categories/api';
import { politicalExposuresApi } from '@entities/political-exposures/api';
import { useIsCountry } from '@hooks/system';
import { useTranslation } from 'react-i18next';
import { NumericFormat } from 'react-number-format';

import {
  OccupationCategory,
  PoliticalExposure,
  SupportedCountries,
} from '@/shared/types';

import { UserProfileFormFields } from '../../../shared/config/profile/form';
import { useCompleteProfileForm } from '../hooks/useCompleteProfileForm';

export const ProfileCompletePage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.profilePage);
  const isLtRegion = useIsCountry(SupportedCountries.LT);

  const {
    data: { political_exposures: politicalExposures },
  } = politicalExposuresApi.useSuspensePoliticalExposuresQuery();
  const {
    data: { occupation_categories: occupationCategories },
  } = occupationCategoriesApi.useSuspenseOccupationCategoriesQuery();

  const {
    form,
    visibleFormFieldsMap,
    handleSubmit,
    planningNewDebts,
    futureReducedEarnings,
    overdueDebt,
  } = useCompleteProfileForm();

  return (
    <div>
      <Typography className="order-2 lg:order-1" variant="s">
        {t(LOCIZE_PROFILE_PAGE_KEYS.completeProfile.title)}
      </Typography>
      <Form {...form}>
        <form
          className="mx-auto grid gap-2 w-full mt-6"
          onSubmit={form.handleSubmit(handleSubmit)}
        >
          {visibleFormFieldsMap[UserProfileFormFields.EMAIL] ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.EMAIL}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.emailLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <Input disabled={form.formState.isSubmitting} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {visibleFormFieldsMap[UserProfileFormFields.PHONE] ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.PHONE}
              render={({ field: { onChange, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.phoneLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <PhoneInput
                      disabled={form.formState.isSubmitting}
                      onValueChange={({ value }) => {
                        onChange(value);
                      }}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {visibleFormFieldsMap[UserProfileFormFields.ADDRESS] ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.ADDRESS}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .addressLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <Input disabled={form.formState.isSubmitting} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}
          <div className="grid w-full grid-cols-[repeat(auto-fit,minmax(4rem,1fr))] gap-6">
            {visibleFormFieldsMap[UserProfileFormFields.CITY] ? (
              <FormField
                control={form.control}
                name={UserProfileFormFields.CITY}
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {t(
                        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                          .cityLabel,
                      )}
                    </FormLabel>
                    <FormControl>
                      <Input
                        disabled={form.formState.isSubmitting}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : null}

            {visibleFormFieldsMap[UserProfileFormFields.POST_CODE] ? (
              <FormField
                control={form.control}
                name={UserProfileFormFields.POST_CODE}
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {t(
                        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                          .postCodeLabel,
                      )}
                    </FormLabel>
                    <FormControl>
                      <Input
                        disabled={form.formState.isSubmitting}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : null}
          </div>

          {visibleFormFieldsMap[UserProfileFormFields.POLITICAL_EXPOSURE] ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.POLITICAL_EXPOSURE}
              render={({ field: { onChange, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .politicalExposureLabel,
                    )}
                  </FormLabel>
                  <Select
                    disabled={form.formState.isSubmitting}
                    onValueChange={onChange}
                    value={field.value}
                    info={t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .politicalExposureTooltip,
                    )}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {politicalExposures?.map((politicalExposure) => {
                        if (!politicalExposure) return null;

                        if (!(politicalExposure in PoliticalExposure))
                          return null;

                        return (
                          <SelectItem
                            key={politicalExposure}
                            value={politicalExposure}
                          >
                            {t(
                              // @ts-expect-error need to fix
                              LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                                .politicalExposure[politicalExposure],
                            )}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {visibleFormFieldsMap[UserProfileFormFields.OCCUPATION_CATEGORY] ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.OCCUPATION_CATEGORY}
              render={({ field: { onChange, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .occupationLabel,
                    )}
                  </FormLabel>
                  <Select
                    disabled={form.formState.isSubmitting}
                    onValueChange={onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {occupationCategories?.map((occupationCategory) => {
                        if (!occupationCategory) return null;

                        if (!(occupationCategory in OccupationCategory))
                          return null;

                        return (
                          <SelectItem
                            key={occupationCategory}
                            value={occupationCategory}
                          >
                            {t(
                              // @ts-expect-error need to fix
                              LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                                .occupation[occupationCategory],
                            )}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {visibleFormFieldsMap[UserProfileFormFields.EMPLOYMENT_DATE] ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.EMPLOYMENT_DATE}
              render={({ field: { onChange, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .employmentDateLabel,
                    )}
                  </FormLabel>
                  <Select
                    disabled={form.formState.isSubmitting}
                    onValueChange={onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {EMPLOYMENT_DATE_OPTIONS.map(({ label, value }) => (
                        <SelectItem key={value} value={value}>
                          {t(label)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {visibleFormFieldsMap[UserProfileFormFields.IBAN] ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.IBAN}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.ibanLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <Input
                      disabled={form.formState.isSubmitting}
                      info={t(
                        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                          .ibanTooltip,
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {visibleFormFieldsMap[UserProfileFormFields.NET_INCOME_MONTHLY] ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.NET_INCOME_MONTHLY}
              render={({ field: { onChange, value, name, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      isLtRegion
                        ? LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                            .netIncomeMonthlyLabelLt
                        : LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                            .netIncomeMonthlyLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <NumericFormat
                      {...field}
                      disabled={form.formState.isSubmitting}
                      allowLeadingZeros={false}
                      allowNegative={false}
                      className="w-full"
                      fixedDecimalScale
                      name={`${name}-input`}
                      onValueChange={(v) => {
                        onChange(v.floatValue);
                      }}
                      value={value}
                      after={<Typography className="text-lg">€</Typography>}
                      info={t(
                        isLtRegion
                          ? LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                              .netIncomeMonthlyTooltipLt
                          : LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                              .netIncomeMonthlyTooltip,
                      )}
                      customInput={Input}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {visibleFormFieldsMap[
            UserProfileFormFields.MONTHLY_LIVING_EXPENSES
          ] ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.MONTHLY_LIVING_EXPENSES}
              render={({ field: { onChange, value, name, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .monthlyLivingExpensesLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <NumericFormat
                      {...field}
                      disabled={form.formState.isSubmitting}
                      allowLeadingZeros={false}
                      allowNegative={false}
                      className="w-full"
                      fixedDecimalScale
                      name={`${name}-input`}
                      onValueChange={(v) => {
                        onChange(v.floatValue);
                      }}
                      value={value}
                      after={<Typography className="text-lg">€</Typography>}
                      info={t(
                        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                          .monthlyLivingExpensesTooltip,
                      )}
                      customInput={Input}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {visibleFormFieldsMap[UserProfileFormFields.EXPENDITURE_MONTHLY] ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.EXPENDITURE_MONTHLY}
              render={({ field: { onChange, value, name, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .expenditureMonthlyLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <NumericFormat
                      {...field}
                      disabled={form.formState.isSubmitting}
                      allowLeadingZeros={false}
                      allowNegative={false}
                      className="w-full"
                      fixedDecimalScale
                      name={`${name}-input`}
                      onValueChange={(v) => {
                        onChange(v.floatValue);
                      }}
                      value={value}
                      after={<Typography className="text-lg">€</Typography>}
                      info={t(
                        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                          .expenditureMonthlyLabel,
                      )}
                      customInput={Input}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {visibleFormFieldsMap[UserProfileFormFields.OVERDUE_DEBT] ? (
            <CheckboxWrapper
              className="mb-4 transition-all"
              checkedClassName="mb-0"
              defaultChecked={!!overdueDebt}
              onCheckedChange={(checked) => {
                if (!checked) {
                  form.setValue(UserProfileFormFields.OVERDUE_DEBT, 0);
                }
              }}
              label={t(
                LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                  .overdueDebtCheckboxLabel,
              )}
            >
              <FormField
                control={form.control}
                name={UserProfileFormFields.OVERDUE_DEBT}
                render={({ field: { onChange, value, name, ...field } }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {t(
                        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                          .overdueDebtLabel,
                      )}
                    </FormLabel>
                    <FormControl>
                      <NumericFormat
                        {...field}
                        disabled={form.formState.isSubmitting}
                        autoFocus={
                          !overdueDebt || form.formState.dirtyFields[name]
                        }
                        allowLeadingZeros={false}
                        allowNegative={false}
                        fixedDecimalScale
                        name={`${name}-input`}
                        onValueChange={(v) => {
                          onChange(v.floatValue);
                        }}
                        value={value}
                        after={<Typography className="text-lg">€</Typography>}
                        customInput={Input}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CheckboxWrapper>
          ) : null}

          {visibleFormFieldsMap[UserProfileFormFields.NUMBER_OF_DEPENDENTS] ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.NUMBER_OF_DEPENDENTS}
              render={({ field: { onChange, value, name, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .numberOfDependentsLabel,
                    )}
                  </FormLabel>
                  <FormControl>
                    <NumericFormat
                      {...field}
                      disabled={form.formState.isSubmitting}
                      allowLeadingZeros={false}
                      allowNegative={false}
                      className="w-full"
                      name={`${name}-input`}
                      onValueChange={(v) => {
                        onChange(v.floatValue);
                      }}
                      value={value}
                      customInput={Input}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {visibleFormFieldsMap[UserProfileFormFields.PLANNING_NEW_DEBTS] ? (
            <CheckboxWrapper
              className="mb-4 transition-all"
              checkedClassName="mb-0"
              defaultChecked={!!planningNewDebts}
              onCheckedChange={(checked) => {
                if (!checked) {
                  form.setValue(UserProfileFormFields.PLANNING_NEW_DEBTS, 0);
                }
              }}
              label={t(
                LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                  .planningNewDebtsCheckboxLabel,
              )}
            >
              <FormField
                control={form.control}
                name={UserProfileFormFields.PLANNING_NEW_DEBTS}
                render={({ field: { onChange, value, name, ...field } }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {t(
                        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                          .planningNewDebtsLabel,
                      )}
                    </FormLabel>
                    <FormControl>
                      <NumericFormat
                        {...field}
                        disabled={form.formState.isSubmitting}
                        autoFocus
                        allowLeadingZeros={false}
                        allowNegative={false}
                        fixedDecimalScale
                        name={`${name}-input`}
                        onValueChange={(v) => {
                          onChange(v.floatValue);
                        }}
                        value={value}
                        after={<Typography className="text-lg">€</Typography>}
                        customInput={Input}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CheckboxWrapper>
          ) : null}

          {visibleFormFieldsMap[
            UserProfileFormFields.FUTURE_REDUCED_EARNINGS
          ] ? (
            <CheckboxWrapper
              className="mb-4 transition-all"
              checkedClassName="mb-0"
              defaultChecked={!!futureReducedEarnings}
              onCheckedChange={(checked) => {
                if (!checked) {
                  form.setValue(
                    UserProfileFormFields.FUTURE_REDUCED_EARNINGS,
                    0,
                  );
                }
              }}
              label={t(
                LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                  .futureReducedEarningsCheckboxLabel,
              )}
            >
              <FormField
                control={form.control}
                name={UserProfileFormFields.FUTURE_REDUCED_EARNINGS}
                render={({ field: { onChange, value, name, ...field } }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {t(
                        LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                          .futureReducedEarningsCheckboxLabel,
                      )}
                    </FormLabel>
                    <FormControl>
                      <NumericFormat
                        {...field}
                        disabled={form.formState.isSubmitting}
                        autoFocus
                        allowLeadingZeros={false}
                        allowNegative={false}
                        fixedDecimalScale
                        name={`${name}-input`}
                        onValueChange={(v) => {
                          onChange(v.floatValue);
                        }}
                        value={value}
                        after={<Typography className="text-lg">€</Typography>}
                        customInput={Input}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CheckboxWrapper>
          ) : null}

          {visibleFormFieldsMap[
            UserProfileFormFields.ULTIMATE_BENEFICIAL_OWNER
          ] ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.ULTIMATE_BENEFICIAL_OWNER}
              render={({ field: { onChange, ...field } }) => (
                <FormItem className="w-full">
                  <FormLabel>
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .ultimateBeneficialOwnerLabel,
                    )}
                  </FormLabel>
                  <Select
                    info={t(
                      LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo
                        .ultimateBeneficialOwnerTooltip,
                    )}
                    disabled={form.formState.isSubmitting}
                    onValueChange={onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={'1'}>
                        {t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.yes)}
                      </SelectItem>
                      <SelectItem value={'0'}>
                        {t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.no)}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : null}

          {visibleFormFieldsMap[UserProfileFormFields.NEWSLETTER_AGREEMENT] ? (
            <FormField
              control={form.control}
              name={UserProfileFormFields.NEWSLETTER_AGREEMENT}
              render={({ field: { onChange, value, ...field } }) => (
                <div className="p-6 mb-8 border-neutral-200 border rounded-3xl">
                  <p className="flex items-center justify-between gap-2">
                    <Typography affects="semibold" tag="span">
                      {t(
                        LOCIZE_PROFILE_PAGE_KEYS.subscribeToNewsletterSection
                          .title,
                      )}
                    </Typography>
                    <Switch
                      disabled={form.formState.isSubmitting}
                      {...field}
                      checked={value}
                      onCheckedChange={onChange}
                    />
                  </p>
                  <Typography className="mt-4" variant="text-s">
                    {t(
                      LOCIZE_PROFILE_PAGE_KEYS.subscribeToNewsletterSection
                        .description,
                    )}
                  </Typography>
                </div>
              )}
            />
          ) : null}

          <Button
            className="w-fit mb-20"
            loading={
              form.formState.isSubmitting || form.formState.isSubmitSuccessful
            }
            disabled={!form.formState.isDirty}
            type="submit"
          >
            {t(LOCIZE_PROFILE_PAGE_KEYS.personalFinancialInfo.saveButton)}
          </Button>
        </form>
      </Form>
    </div>
  );
};
