import { LOCIZE_COMMON_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_PROFILE_PAGE_KEYS } from '@config/locize/profile-page';
import { ROUTE_NAMES } from '@config/routes';
import { userApi } from '@entities/user';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@hooks/system';
import { useQueryClient } from '@tanstack/react-query';
import { getRouteApi, useBlocker } from '@tanstack/react-router';
import { filterObjectByExistingKeysInObject } from '@utils/filterObjectByExistingKeysInObject';
import { processGqlFormValidationErrors } from '@utils/parseGraphQLError';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useUpdateEffect } from 'react-use';

import { USER_PROFILE_FORM_DEFAULT_VALUES } from '../../../shared/config/profile/form';
import {
  CompleteProfileFormSchema,
  type CompleteProfileFormType,
} from '../schemes';
import { useFormValues } from './useFormValues';
import { useVisibleFormFields } from './useVisibleFormFields';

const routeApi = getRouteApi('/_protected/_main/profile/complete');

export const useCompleteProfileForm = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.profilePage);
  const { t: tc } = useTranslation(LOCIZE_NAMESPACES.common);

  const navigate = routeApi.useNavigate();
  const queryClient = useQueryClient();
  const { data: userProfileData } = userApi.useSuspenseUserProfileInfoQuery();
  const { data: userData } = userApi.useSuspenseUserQuery();

  const { mutateAsync } = userApi.useUpdateUserMutation({
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: userApi.useSuspenseUserProfileInfoQuery.getKey(),
      });
      await queryClient.invalidateQueries({
        queryKey: userApi.useSuspenseUserQuery.getKey(),
      });
    },
  });
  const { showErrorMessage, showSuccessMessage } = useToast();

  const values = useFormValues(userData, userProfileData);
  const visibleFormFieldsMap = useVisibleFormFields(userData, userProfileData);

  const form = useForm<CompleteProfileFormType>({
    resolver: zodResolver(CompleteProfileFormSchema),
    defaultValues: USER_PROFILE_FORM_DEFAULT_VALUES,
    values,
  });

  const handleSubmit = async (formData: CompleteProfileFormType) => {
    if (!userData?.me?.id) throw new Error('User is not defined');

    const filteredFormData = filterObjectByExistingKeysInObject({
      objectToFilter: {
        address: formData.address,
        postCode: formData.postCode,
        city: formData.city,
        politicalExposure: formData.politicalExposure,
        occupationCategory: formData.occupationCategory,
        employmentDate: formData.employmentDate,
        iban: formData.iban,
        netIncomeMonthly: formData.netIncomeMonthly,
        numberOfDependents: formData.numberOfDependents,
        monthlyLivingExpenses: formData.monthlyLivingExpenses,
        expenditureMonthly: formData.expenditureMonthly,
        ultimateBeneficialOwner: !!+formData.ultimateBeneficialOwner,
        newsletterAgreement: !!+formData.newsletterAgreement,
      },
      object: visibleFormFieldsMap,
      condition: (key, map) => !!map[key],
    });

    try {
      await mutateAsync({
        userId: userData?.me?.id,
        phone: formData.phone,
        email: formData.email,
        ...filteredFormData,
      });
    } catch (error) {
      if (Array.isArray(error)) {
        processGqlFormValidationErrors({
          error,
          setFormError: form.setError,
        });
        return;
      }

      showErrorMessage();
    }
  };

  useBlocker({
    blockerFn: () =>
      window.confirm(tc(LOCIZE_COMMON_KEYS.leaveWithoutSavingPrompt)),
    condition: !form.formState.isSubmitSuccessful && form.formState.isDirty,
  });

  useUpdateEffect(() => {
    if (form.formState.isSubmitSuccessful) {
      showSuccessMessage(t(LOCIZE_PROFILE_PAGE_KEYS.profileUpdatedToast));

      navigate({
        to: ROUTE_NAMES.profile,
        replace: true,
      });
    }
  });

  return {
    form,
    visibleFormFieldsMap,
    handleSubmit,
    planningNewDebts: userProfileData?.me?.profile?.planning_new_debts,
    futureReducedEarnings:
      userProfileData?.me?.profile?.future_reduced_earnings,
    overdueDebt: userProfileData?.me?.profile?.overdue_debt,
  };
};
