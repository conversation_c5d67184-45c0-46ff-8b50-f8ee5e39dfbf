import { Typography } from '@components/typography';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_INVOICE_PAYMENT_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_AGREEMENT_PAYMENT_KEYS } from '@config/locize/agreement-payment';
import { useAgreementPaymentCalculation } from '@pages/agreement-payment/hooks/useAgreementPaymentCalculation';
import { useAgreementPaymentInfo } from '@pages/agreement-payment/hooks/useAgreementPaymentInfo';
import { getRouteApi } from '@tanstack/react-router';
import { formatDate, formatNumber } from '@utils/formatters';
import debounce from 'lodash/debounce';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { type NumberFormatValues, NumericFormat } from 'react-number-format';

const routeApi = getRouteApi('/payment/agreement/');

const USER_TYPE_DEBOUNCE_TIME = 800;

export const AgreementPaymentInfo = () => {
  const [amount, setAmount] = useState(0);

  const { t } = useTranslation(LOCIZE_NAMESPACES.agreementPayment);

  const { referenceKey, amount: amountFromSearch } = routeApi.useSearch();
  const navigate = routeApi.useNavigate();

  const agreementPaymentInfo = useAgreementPaymentInfo({
    applicationReferenceKey: referenceKey,
  });

  const { data: payment, isFetching } = useAgreementPaymentCalculation();

  const onAmountChange = (v: NumberFormatValues) => {
    if (v.floatValue === amount) {
      return;
    }

    // This is a hack to set the value back to the previous value if we have triggered the same calculation before
    if (v.floatValue === amountFromSearch) {
      setTimeout(() => setAmount(amount - 1), USER_TYPE_DEBOUNCE_TIME);
      setTimeout(() => setAmount(amount), USER_TYPE_DEBOUNCE_TIME + 5);
    }

    navigate({
      replace: true,
      search: (prev) => ({
        ...prev,
        amount: v.floatValue,
      }),
    });
  };

  const debouncedOnChange = debounce(onAmountChange, USER_TYPE_DEBOUNCE_TIME);

  const isFetchingWithFee = Boolean(
    payment?.card_payment_fee?.card_payment_fee_pct ? isFetching : false,
  );

  useEffect(() => {
    setAmount(
      (payment?.card_payment_fee?.card_payment_fee_pct
        ? payment.card_payment_fee.amount_with_fee
        : payment?.card_payment_fee?.amount) || 0,
    );
  }, [payment]);

  return (
    <div className="space-y-12 grid w-full mb-8 md:mb-0 md:max-w-[25rem]">
      <div className="grid rounded-[.875rem] overflow-hidden border border-neutral-200 bg-primary-white">
        <div className="bg-neutral-700 px-6 py-[1.125rem] flex items-center justify-between gap-4">
          <Typography
            affects="semibold"
            tag="span"
            className="text-neutral-400"
          >
            {t(LOCIZE_INVOICE_PAYMENT_KEYS.infoTitle)}
          </Typography>
        </div>

        {agreementPaymentInfo ? (
          <div className="grid py-[18px] divide-y-[1px] divide-neutral-200 [&>div:first-child]:pt-0 [&>div:last-child]:pb-0">
            <div className="flex items-center justify-between gap-2 py-[18px] px-[18px]">
              <Typography variant="text-s" className="text-neutral-500">
                {t(LOCIZE_AGREEMENT_PAYMENT_KEYS.totalOutstanding)}
              </Typography>
              <Typography variant="text-s">
                {formatNumber({
                  value: agreementPaymentInfo?.totalOutstanding,
                  minimumFractionDigits: 2,
                })}{' '}
                €
              </Typography>
            </div>
            <div className="flex items-center justify-between gap-2 py-[18px] px-[18px]">
              <Typography variant="text-s" className="text-neutral-500">
                {t(LOCIZE_AGREEMENT_PAYMENT_KEYS.nextMonthlyPayment)}
              </Typography>
              <Typography variant="text-s">
                {formatNumber({
                  value: agreementPaymentInfo?.nextMonthlyPayment,
                  minimumFractionDigits: 2,
                })}{' '}
                €
              </Typography>
            </div>
            <div className="flex items-center justify-between gap-2 py-[18px] px-[18px]">
              <Typography variant="text-s" className="text-neutral-500">
                {t(LOCIZE_AGREEMENT_PAYMENT_KEYS.nextPaymentDate)}
              </Typography>
              <Typography variant="text-s">
                {formatDate(agreementPaymentInfo?.nextPaymentDate)}
              </Typography>
            </div>
            {payment?.card_payment_fee ? (
              <>
                <div className="flex items-center justify-between gap-2 py-[18px] px-[18px]">
                  <div className="w-full">
                    <Typography
                      variant="text-s"
                      affects="medium"
                      className="mb-1.5"
                    >
                      {t(LOCIZE_AGREEMENT_PAYMENT_KEYS.repaymentAmount)}
                    </Typography>
                    <div className="relative w-full">
                      <NumericFormat
                        allowedDecimalSeparators={[',', '.']}
                        thousandSeparator=","
                        allowLeadingZeros={false}
                        allowNegative={false}
                        className="text-[1rem] leading-[1.5rem] tracking-[-.02rem] bg-neutral-50 py-[.6875rem] px-3.5 pr-8 flex-1 w-full rounded-1xl focus-visible:outline-none box-border border border-transparent ring-offset-background placeholder:text-neutral-400 disabled:cursor-not-allowed disabled:bg-neutral-100 disabled:text-neutral-400 focus-visible:border-primary-black"
                        fixedDecimalScale
                        onValueChange={debouncedOnChange}
                        disabled={isFetchingWithFee}
                        defaultValue={agreementPaymentInfo?.nextMonthlyPayment}
                        value={amountFromSearch}
                      />
                      <Typography className="absolute top-0 right-[.875rem] translate-y-[50%] text-neutral-400">
                        €
                      </Typography>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between gap-2 py-[18px] px-[18px]">
                  <Typography variant="text-s" className="text-neutral-500">
                    {payment?.card_payment_fee?.card_payment_fee_pct
                      ? t(LOCIZE_AGREEMENT_PAYMENT_KEYS.totalAndFee, {
                          fee: payment.card_payment_fee.card_payment_fee_pct,
                        })
                      : t(LOCIZE_AGREEMENT_PAYMENT_KEYS.total)}
                  </Typography>
                  {isFetchingWithFee ? (
                    <Skeleton className="h-[1.25rem] w-14" />
                  ) : (
                    <Typography variant="text-s">
                      {formatNumber({
                        value: amount,
                        minimumFractionDigits: 2,
                      })}{' '}
                      €
                    </Typography>
                  )}
                </div>
              </>
            ) : null}
          </div>
        ) : (
          <AgreementPaymentInfoSkeleton />
        )}
      </div>
    </div>
  );
};

function AgreementPaymentInfoSkeleton() {
  const { t } = useTranslation(LOCIZE_NAMESPACES.agreementPayment);

  return (
    <div className="grid py-[18px] divide-y-[1px] divide-neutral-200 [&>div:first-child]:pt-0 [&>div:last-child]:pb-0">
      <div className="flex items-center justify-between gap-2 py-[18px] px-[18px]">
        <Typography variant="text-s" className="text-neutral-500">
          {t(LOCIZE_AGREEMENT_PAYMENT_KEYS.totalOutstanding)}
        </Typography>
        <Skeleton className="h-[1.25rem] w-14" />
      </div>
      <div className="flex items-center justify-between gap-2 py-[18px] px-[18px]">
        <Typography variant="text-s" className="text-neutral-500">
          {t(LOCIZE_AGREEMENT_PAYMENT_KEYS.nextMonthlyPayment)}
        </Typography>
        <Skeleton className="h-[1.25rem] w-14" />
      </div>
      <div className="flex items-center justify-between gap-2 py-[18px] px-[18px]">
        <Typography variant="text-s" className="text-neutral-500">
          {t(LOCIZE_AGREEMENT_PAYMENT_KEYS.nextPaymentDate)}
        </Typography>
        <Skeleton className="h-[1.25rem] w-14" />
      </div>
      <div className="flex items-center justify-between gap-2 py-[18px] px-[18px]">
        <div className="w-full">
          <Typography tag="div" variant="text-s" className="mb-1.5">
            <Skeleton className="h-[1.25rem] w-14" />
          </Typography>
          <Skeleton className="h-[3rem] w-full border border-transparent rounded-1xl" />
        </div>
      </div>
      <div className="flex items-center justify-between gap-2 py-[18px] px-[18px]">
        <Typography variant="text-s" className="text-neutral-500">
          {t(LOCIZE_AGREEMENT_PAYMENT_KEYS.total)}
        </Typography>
        <Skeleton className="h-[1.25rem] w-14" />
      </div>
    </div>
  );
}
