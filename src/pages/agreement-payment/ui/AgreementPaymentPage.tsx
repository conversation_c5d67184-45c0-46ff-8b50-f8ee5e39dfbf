import { Helmet } from '@components/Helmet';
import { LateSuspense } from '@components/LateSuspense';
import { LoadingSpinner } from '@components/LoadingSpinner';
import { Button } from '@components/ui/button';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_AGREEMENT_PAYMENT_KEYS } from '@config/locize/agreement-payment';
import { ROUTE_NAMES } from '@config/routes';
import { AppPaymentMethod } from '@entities/payments';
import { useIsUserAuthorized } from '@entities/user';
import {
  type PaymentMethodsSelectProps,
  PaymentMethodsToggleGroup,
} from '@features/payment-methods-select';
import { useIsMobileView } from '@hooks/system';
import { useAgreementPaymentCalculation } from '@pages/agreement-payment/hooks/useAgreementPaymentCalculation';
import { getRoute<PERSON><PERSON>, Link } from '@tanstack/react-router';
import { PaymentAuthHeader } from '@widgets/headers/payment-auth-header';
import { DualPanelLayout } from '@widgets/layouts/dual-panel';
import { lazy, type ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

import { AgreementPaymentFormView } from '../config';
import { AgreementPaymentInfo } from './agreement-payment-info';

const AgreementPaymentManualTransferInfo = lazy(() =>
  import('./agreement-payment-manual-transfer-info').then(
    ({ AgreementPaymentManualTransferInfo }) => ({
      default: AgreementPaymentManualTransferInfo,
    }),
  ),
);
const BanklinkPaymentForm = lazy(() =>
  import('@widgets/banklink-payment').then(({ BanklinkPaymentForm }) => ({
    default: BanklinkPaymentForm,
  })),
);
const CreditCardPaymentSelect = lazy(() =>
  import('@features/credit-card-payment').then(
    ({ CreditCardPaymentSelect }) => ({
      default: CreditCardPaymentSelect,
    }),
  ),
);

const routeApi = getRouteApi('/payment/agreement/');

const paymentMethodRenderer: Record<
  AppPaymentMethod,
  (data: {
    applicationId?: number;
    amount: number;
    redirectUrl: string;
    onCancel?: () => void;
  }) => ReactNode
> = {
  [AppPaymentMethod.BANKLINK]: (data) => <BanklinkPaymentForm {...data} />,
  [AppPaymentMethod.CARD]: (data) => <CreditCardPaymentSelect {...data} />,
  [AppPaymentMethod.MANUAL_TRANSFER]: ({ onCancel }) => (
    <AgreementPaymentManualTransferInfo onCancel={onCancel} />
  ),
};

export const AgreementPaymentPage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.agreementPayment);

  const { data: isAuthorized } = useIsUserAuthorized();

  const isMobileView = useIsMobileView();

  return (
    <>
      <Helmet title={t(LOCIZE_AGREEMENT_PAYMENT_KEYS.pageTitle)} />
      <DualPanelLayout
        fromPage={ROUTE_NAMES.agreements}
        header={!isAuthorized ? <PaymentAuthHeader /> : undefined}
        left={isMobileView ? null : <AgreementPaymentInfo />}
        right={<AgreementPaymentRightPanel />}
      />
    </>
  );
};

function AgreementPaymentRightPanel() {
  const { t } = useTranslation(LOCIZE_NAMESPACES.agreementPayment);
  const { data: payment } = useAgreementPaymentCalculation();
  const isMobileView = useIsMobileView();
  const { paymentMethod, formView } = routeApi.useSearch();
  const navigate = routeApi.useNavigate();
  const { applicationId } = routeApi.useRouteContext();

  const onPaymentMethodChange: PaymentMethodsSelectProps['onValueChange'] = (
    value,
  ) => {
    navigate({
      replace: true,
      search: (prev) => ({
        ...prev,
        paymentMethod: value,
      }),
    });
  };

  const onCancelPaymentMethod = () => {
    navigate({
      replace: true,
      search: (prev) => ({
        ...prev,
        formView: AgreementPaymentFormView.OVERVIEW,
      }),
    });
  };

  return (
    <div className="w-full max-w-[25rem] flex flex-col h-full [&>h1]:mb-10 [&>h1]:md:mt-6 [&>h1]:md:mb-8">
      <div className="flex flex-col md:p-0 md:flex-1 md:justify-center md:max-h-[689px]">
        <LateSuspense fallback={<LoadingSpinner className="mx-auto" />}>
          {formView === AgreementPaymentFormView.OVERVIEW ? (
            <>
              {isMobileView ? (
                <>
                  <AgreementPaymentInfo />
                  <Button className="mt-14" asChild>
                    <Link
                      to={ROUTE_NAMES.current}
                      search={(prev) => ({
                        ...prev,
                        formView: AgreementPaymentFormView.PAYMENT,
                      })}
                    >
                      {t(LOCIZE_AGREEMENT_PAYMENT_KEYS.paymentMethod)}
                    </Link>
                  </Button>
                </>
              ) : (
                <>
                  <PaymentMethodsToggleGroup
                    className="mb-8"
                    value={paymentMethod}
                    onValueChange={onPaymentMethodChange}
                  />
                  {payment?.card_payment_fee ? (
                    paymentMethodRenderer[paymentMethod]({
                      applicationId,
                      amount: payment.card_payment_fee.amount,
                      redirectUrl: `${window.origin}${ROUTE_NAMES.paymentAgreement}`,
                    })
                  ) : (
                    // TODO: Add loading state
                    <LoadingSpinner className="mx-auto" />
                  )}
                </>
              )}
            </>
          ) : payment?.card_payment_fee ? (
            <>
              <PaymentMethodsToggleGroup
                className="mb-8"
                value={paymentMethod}
                onValueChange={onPaymentMethodChange}
              />
              {paymentMethodRenderer[paymentMethod]({
                applicationId,
                amount: payment.card_payment_fee.amount,
                redirectUrl: `${window.origin}${ROUTE_NAMES.paymentAgreement}`,
                onCancel: isMobileView ? onCancelPaymentMethod : undefined,
              })}
            </>
          ) : (
            // TODO: Add loading state
            <LoadingSpinner className="mx-auto" />
          )}
        </LateSuspense>
      </div>
    </div>
  );
}
