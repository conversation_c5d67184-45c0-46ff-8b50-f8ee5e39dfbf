import { Typography } from '@components/typography';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@components/ui/accordion';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_INSURANCE_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useIsMobileView } from '@hooks/system';
import ChevronIcon from '@icons/chevron.svg?react';
import ShieldGreenIcon from '@icons/shield-green.svg?react';
import { cn } from '@utils/tailwind';
import { ArrowRightIcon, FileTextIcon } from 'lucide-react';
import { lazy, Suspense } from 'react';
import { useTranslation } from 'react-i18next';

import insurancePartner from '/images/insurance/seesam-insurance-logo.webp';

import { INSURANCE_FAQ, PHONE_IMAGES_BY_LANGUAGE } from '../config';
import {
  generateInsuranceAgentInformationDownloadUrl,
  generateInsuranceServiceTermsDownloadUrl,
} from '../utils';
import { ApplyInsuranceBtn } from './ApplyInsuranceBtn';

const InsuranceBanner = lazy(() =>
  import('./InsuranceBanner').then((module) => ({
    default: module.InsuranceBanner,
  })),
);

type InsuranceInfoProps = {
  bannerVisible?: boolean;
  getStartedButtonVisible?: boolean;
};

export const InsuranceInfo = ({
  bannerVisible,
  getStartedButtonVisible,
}: InsuranceInfoProps) => {
  const isMobile = useIsMobileView();
  const { t } = useTranslation(LOCIZE_NAMESPACES.insurance);
  const { i18n } = useTranslation();

  const renderInfoDots = () => {
    return (
      <div className="relative space-y-6">
        <div className="absolute top-8 left-[10px] h-[69%] border-black border-l-2 border-dashed" />

        {[
          t(LOCIZE_INSURANCE_KEYS.insurancePageStep1),
          t(LOCIZE_INSURANCE_KEYS.insurancePageStep2),
          t(LOCIZE_INSURANCE_KEYS.insurancePageStep3),
        ].map((text) => (
          <div key={Math.random()} className="flex items-start space-x-3">
            <div className="relative flex min-h-6 min-w-6 items-center justify-center rounded-full border-2 border-black bg-white">
              <div className="h-3 w-3 rounded-full bg-green-500" />
            </div>
            <Typography variant="text-m">{text}</Typography>
          </div>
        ))}
      </div>
    );
  };

  const renderInfoImgs = () => {
    return (
      <div className="relative top-0 left-0 flex w-[24.25rem] min-w-[24.25rem] flex-col self-center md:w-[26.25rem] md:min-w-[26.25rem] lg:flex-1 lg:self-end">
        <div className="h-[14.8rem] md:h-[17rem]">
          {Object.keys(PHONE_IMAGES_BY_LANGUAGE).map((position) => {
            return (
              <div
                key={position}
                className={
                  position === 'Second'
                    ? 'absolute z-10 w-[11rem] right-9 lg:right-4 md:w-[13.75rem]'
                    : 'absolute top-[4rem] w-[11rem] left-9 md:w-[13.75rem]'
                }
              >
                <Suspense fallback={<Skeleton className="h-[100rem] w-full" />}>
                  <img
                    src={PHONE_IMAGES_BY_LANGUAGE[position][i18n.language]}
                    alt="insurance-info-img"
                    className="h-full w-full object-contain"
                    loading="lazy"
                  />
                </Suspense>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderInfoContainers = () => {
    const INFO_ITEMS = [
      {
        title: LOCIZE_INSURANCE_KEYS.insuranceJobLossTitle,
        description1: LOCIZE_INSURANCE_KEYS.insuranceJobLossDescription1,
        description2: LOCIZE_INSURANCE_KEYS.insuranceJobLossDescription2,
        className: '',
      },
      {
        title: LOCIZE_INSURANCE_KEYS.insuranceWorkAbilityTitle,
        description1: LOCIZE_INSURANCE_KEYS.insuranceWorkAbilityDescription1,
        description2: LOCIZE_INSURANCE_KEYS.insuranceWorkAbilityDescription2,
        className: 'border-neutral-200 border-t lg:border-t-0 lg:border-l',
      },
    ] as const;

    return (
      <div className="flex flex-col bg-primary-white border-neutral-200 border-x-0 border lg:rounded-[.875rem] lg:border-x lg:flex-row">
        {INFO_ITEMS.map((item) => (
          <div
            key={item.title}
            className={cn('flex flex-col flex-1 p-4', item.className)}
          >
            <div className="mb-4">
              <ShieldGreenIcon />
            </div>
            <Typography className="mb-4" variant="xs" affects="semibold">
              {t(item.title)}
            </Typography>
            <Typography className="mb-4" variant="text-s">
              {t(item.description1)}
            </Typography>
            <Typography variant="text-s" className="mt-auto text-neutral-500">
              {t(item.description2)}
            </Typography>
          </div>
        ))}
      </div>
    );
  };

  const renderFaq = () => {
    return (
      <div className="mt-12">
        <Typography variant={isMobile ? 'xs' : 'm'} affects="semibold">
          {t(LOCIZE_INSURANCE_KEYS.insuranceFaqTitle)}
        </Typography>
        <div className="bg-neutral-50 rounded-2xl pt-0 pb-8 px-8 mt-8">
          {INSURANCE_FAQ.map((item) => (
            <Accordion
              key={item.question}
              type="multiple"
              className="gap-4 flex flex-col"
            >
              <AccordionItem
                className="border-0 border-b border-b-neutral-200 border-solid rounded-none"
                value={item.question}
              >
                <AccordionTrigger
                  openIcon={
                    <ChevronIcon className="plus hidden size-6 shrink-0 rotate-180" />
                  }
                  closeIcon={
                    <ChevronIcon className="minus hidden size-6 shrink-0" />
                  }
                  className="px-0"
                >
                  <Typography affects="semibold">{t(item.question)}</Typography>
                </AccordionTrigger>
                <AccordionContent className="pl-0">
                  <Typography>{t(item.answer)}</Typography>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          ))}
        </div>
      </div>
    );
  };

  return (
    <>
      {bannerVisible && (
        <Suspense fallback={<Skeleton className="h-[100rem] w-full" />}>
          <InsuranceBanner />
        </Suspense>
      )}
      <Typography
        variant={isMobile ? 'xs' : 'm'}
        affects="semibold"
        className="my-[4rem]"
      >
        {t(LOCIZE_INSURANCE_KEYS.insurancePageDescription)}
      </Typography>

      <div className="flex flex-col overflow-hidden gap-6 md:flex-col lg:flex-row">
        <div className="flex flex-col gap-4 mb-10">
          <Typography variant="xs" affects="bold">
            {t(LOCIZE_INSURANCE_KEYS.insurancePageStepsTitle)}
          </Typography>

          {renderInfoDots()}

          {getStartedButtonVisible && (
            <ApplyInsuranceBtn className="mt-10 w-fit" variant={'black'} />
          )}
        </div>

        {renderInfoImgs()}
      </div>

      {renderInfoContainers()}

      <div className="flex flex-col gap-6 shrink-0 mt-5 md:flex-col-reverse">
        <div className="border-neutral-200 rounded-[.875rem] p-8 gap-4 flex flex-col items-center md:flex-row justify-between md:border">
          <div className="flex flex-col gap-4 text-center md:text-left">
            <Typography variant="text-s" className="text-neutral-500">
              {t(LOCIZE_INSURANCE_KEYS.insurancePartnerTitle)}
            </Typography>
            <Typography variant="s">
              {t(LOCIZE_INSURANCE_KEYS.insurancePartnerDescription)}
            </Typography>
          </div>
          <div className="shrink-0 w-[8.5rem] md:w-[10rem]">
            <img
              src={insurancePartner}
              alt="insurance-partner-img"
              className="w-full h-auto"
            />
          </div>
        </div>

        {getStartedButtonVisible && (
          <ApplyInsuranceBtn
            className="w-full"
            variant={isMobile ? 'grey' : 'black'}
          />
        )}
      </div>

      <div className="flex flex-col gap-2 mt-16">
        <Typography
          variant={isMobile ? 'xs' : 'm'}
          affects="semibold"
          className="mb-4"
        >
          {t(LOCIZE_INSURANCE_KEYS.insuranceUsefulInfoTitle)}
        </Typography>

        <a
          target="_blank"
          href={generateInsuranceAgentInformationDownloadUrl({
            language: i18n.language,
          })}
          rel="noreferrer"
          className="bg-neutral-50 rounded-2xl py-4 px-8 flex flex-row justify-between cursor-pointer border-t border-t-transparent transition-colors duration-200 ease-in-out hover:bg-neutral-100 hover:border-t-neutral-200"
        >
          <div className="flex flex-row gap-2 items-center">
            <FileTextIcon className="size-4" />
            <Typography variant="text-m">
              {t(LOCIZE_INSURANCE_KEYS.insuranceInsuranceAgentInformation)}
            </Typography>
          </div>
          <ArrowRightIcon className="size-4 hover:text-neutral-700" />
        </a>

        <a
          target="_blank"
          href={generateInsuranceServiceTermsDownloadUrl({
            language: i18n.language,
          })}
          rel="noreferrer"
          className="bg-neutral-50 rounded-2xl py-4 px-8 flex flex-row justify-between cursor-pointer border-t border-t-transparent transition-colors duration-200 ease-in-out hover:bg-neutral-100 hover:border-t-neutral-200"
        >
          <div className="flex flex-row gap-2 items-center">
            <FileTextIcon className="size-4" />
            <Typography variant="text-m">
              {t(LOCIZE_INSURANCE_KEYS.insuranceTermsOfService)}
            </Typography>
          </div>
          <ArrowRightIcon className="size-4 hover:text-neutral-700" />
        </a>
      </div>

      {renderFaq()}
    </>
  );
};
