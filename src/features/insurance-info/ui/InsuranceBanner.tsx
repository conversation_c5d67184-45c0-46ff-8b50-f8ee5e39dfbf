import { Typography } from '@components/typography';
import { LOCIZE_INSURANCE_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useIsMobileView } from '@hooks/system';
import { useTranslation } from 'react-i18next';

import insuranceBanner from '/images/dashboard/premiun-carousel/insurance.webp';

import { ApplyInsuranceBtn } from './ApplyInsuranceBtn';

export const InsuranceBanner = () => {
  const isMobile = useIsMobileView();
  const { t } = useTranslation(LOCIZE_NAMESPACES.insurance);

  return (
    <>
      {isMobile ? (
        <div className="flex flex-col gap-8">
          <img
            src={insuranceBanner}
            alt="insurance-banner-img"
            className="w-full h-[9.5rem] object-cover rounded-[.875rem]"
          />
          <Typography variant="m">
            {t(LOCIZE_INSURANCE_KEYS.insurancePageTitle)}
          </Typography>
          <Typography variant="text-l">
            {t(LOCIZE_INSURANCE_KEYS.insuranceBannerDescription)}
          </Typography>

          <ApplyInsuranceBtn className="w-fit" variant="black" />
        </div>
      ) : (
        <div className="flex h-[18rem]">
          <div
            className="flex items-center h-full w-full rounded-2xl bg-center bg-cover"
            style={{
              backgroundImage: `url(${insuranceBanner})`,
            }}
          >
            <div className="flex flex-col justify-center p-10 gap-4">
              <Typography
                variant="xl"
                className="text-5xl text-primary-white lg:text-6xl"
              >
                {t(LOCIZE_INSURANCE_KEYS.insurancePageTitle)}
              </Typography>
              <Typography variant="text-l" className="text-primary-white">
                {t(LOCIZE_INSURANCE_KEYS.insuranceBannerDescription)}
              </Typography>

              <ApplyInsuranceBtn className="w-fit" variant="black" />
            </div>
          </div>
        </div>
      )}
    </>
  );
};
