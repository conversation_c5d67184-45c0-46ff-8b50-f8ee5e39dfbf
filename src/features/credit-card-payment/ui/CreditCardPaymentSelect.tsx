import { Button } from '@components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@components/ui/select';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_NAMESPACES, LOCIZE_PAYMENT_KEYS } from '@config/locize';
import { paymentsApi } from '@entities/payments';
import { creditCardPaymentApi } from '@features/credit-card-payment/api';
import { useToast } from '@hooks/system';
import { type FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  EverypayPaymentTransactionType,
  PaymentCategoryType,
} from '@/shared/types';

const ADD_NEW_CARD_OPTION = 'Add new card';

type CreditCardPaymentSelectProps = {
  amount: number;
  onCancel?: () => void;
  redirectUrl: string;
  userId?: number;
  applicationId?: number;
  creditAccountId?: number;
};

export const CreditCardPaymentSelect: FC<CreditCardPaymentSelectProps> = ({
  amount,
  onCancel,
  redirectUrl,
  userId,
  applicationId,
  creditAccountId,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.payment);

  const { data, isFetching } = creditCardPaymentApi.useUserCreditCardQuery();

  const { showErrorMessage } = useToast();
  const [selectedCardCcToken, setSelectedCardCcToken] =
    useState<string>(ADD_NEW_CARD_OPTION);

  const createEverypayPaymentMutation =
    paymentsApi.useStartEverypayPaymentMutation({
      onSuccess: (data) => {
        if (!data?.payment?.payment_link) {
          // TODO: Update error message
          showErrorMessage('Something went wrong. Please try again later');

          return;
        }

        window.open(data.payment.payment_link, '_self');
      },
    });

  const onSubmit = () => {
    const isAddNewCardOptionSelected =
      selectedCardCcToken === ADD_NEW_CARD_OPTION;

    createEverypayPaymentMutation.mutate({
      redirectUrl,
      userId,
      applicationId,
      creditAccountId,
      amount,
      ccToken: isAddNewCardOptionSelected ? undefined : selectedCardCcToken,
      requestCcToken: isAddNewCardOptionSelected,
      paymentCategory: PaymentCategoryType.REGULAR,
      transactionType: isAddNewCardOptionSelected
        ? EverypayPaymentTransactionType.ONEOFF
        : EverypayPaymentTransactionType.CIT,
    });
  };

  useEffect(() => {
    const firstCard = data?.me?.everypay_cards?.at(0);

    if (firstCard) {
      setSelectedCardCcToken(firstCard.cc_token);
    }
  }, [data?.me?.everypay_cards]);

  return (
    <div>
      {isFetching ? (
        <Skeleton className="h-12 w-full" />
      ) : (
        <Select
          onValueChange={(value) => setSelectedCardCcToken(value as string)}
          value={selectedCardCcToken}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={ADD_NEW_CARD_OPTION}>
              {t(LOCIZE_PAYMENT_KEYS.addNewCard)}
            </SelectItem>
            {data?.me?.everypay_cards?.map((card) => {
              if (!card) return null;

              return (
                <SelectItem key={card.id} value={card.cc_token}>
                  **** **** **** {card.cc_last_four_digits}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      )}
      <div className="grid gap-4 mt-14">
        <Button
          onClick={onSubmit}
          loading={createEverypayPaymentMutation.isPending}
          disabled={isFetching}
        >
          {t(LOCIZE_PAYMENT_KEYS.payButton)}
        </Button>
        {onCancel ? (
          <Button variant="white" onClick={onCancel}>
            {t(LOCIZE_PAYMENT_KEYS.backButton)}
          </Button>
        ) : null}
      </div>
    </div>
  );
};
