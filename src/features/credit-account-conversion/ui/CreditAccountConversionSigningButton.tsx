import { Typography } from '@components/typography';
import { But<PERSON> } from '@components/ui/button';
import { Checkbox } from '@components/ui/checkbox';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS } from '@config/locize/credit-account-conversion';
import { PURCHASE_FLOW_ROUTE_NAME } from '@config/routes';
import { applicationApi } from '@entities/application/api';
import { useSearch } from '@tanstack/react-router';
import { cn } from '@utils/tailwind';
import { type FC, lazy } from 'react';
import { Trans } from 'react-i18next';
import { useTranslation } from 'react-i18next';
import { useToggle } from 'react-use';

import { ContractType } from '@/shared/types';

import { useCreditAccountConversionSigningHandlers } from '../hooks';
import type { CreditAccountConversionSigningButtonProps } from '../types';
import { generateDeclarationOfIntentUrl } from '../utils';

const DECLARATION_OF_INTENT_CHECKBOX_ID = 'declaration-of-intent-checkbox';

const SigningPasswordButton = lazy(() =>
  import('@features/signing/by-password/ui').then(
    ({ SigningPasswordButton }) => ({
      default: SigningPasswordButton,
    }),
  ),
);

export const CreditAccountConversionSigningButton: FC<
  CreditAccountConversionSigningButtonProps
> = ({ className, isCreditAccountActive = true }) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditAccountConversion);
  const [isChecked, toggle] = useToggle(false);
  const { handleSigningSuccess, handleSigningReject, handleSigningError } =
    useCreditAccountConversionSigningHandlers();
  const { referenceKey } = useSearch({ strict: false });

  if (!referenceKey)
    throw new Error('Application reference key is not found in url');

  const { data: applicationId } = applicationApi.useSuspenseApplicationQuery(
    {
      referenceKey,
    },
    {
      select: (data) => {
        if (!data?.application_by_reference) {
          throw new Error('Application is not received from backend');
        }

        return data.application_by_reference.id;
      },
    },
  );

  if (!isCreditAccountActive) {
    return (
      <>
        <Button fullWidth asChild>
          <a
            href={PURCHASE_FLOW_ROUTE_NAME.creditLineConversion.replace(
              '$applicationReferenceKey',
              referenceKey,
            )}
          >
            <Typography className="text-primary-white">
              {t(LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.convert)}
            </Typography>
          </a>
        </Button>
        <Typography variant="text-s" className="text-center pt-4">
          {t(LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.redirectDisclaimer)}
        </Typography>
      </>
    );
  }

  return (
    <div className={cn('flex flex-col gap-6', className)}>
      <div className={cn('flex items-center mx-auto', 'group')}>
        <Checkbox
          id={DECLARATION_OF_INTENT_CHECKBOX_ID}
          checked={isChecked}
          onCheckedChange={toggle}
        />
        <label
          className="pl-2.5 cursor-pointer"
          htmlFor={DECLARATION_OF_INTENT_CHECKBOX_ID}
        >
          <Trans
            components={{
              termsLink: (
                <a
                  className={cn('underline', 'group-hover:opacity-70')}
                  href={generateDeclarationOfIntentUrl(applicationId)}
                />
              ),
            }}
            i18nKey={LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.termsCheckboxLabel}
            t={t}
          />
        </label>
      </div>
      <SigningPasswordButton
        disabled={!isChecked}
        className="w-full"
        label={t(LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.convert)}
        applicationId={applicationId}
        contractType={ContractType.HP_CA_CONVERSION_DECLARATION_OF_INTENT}
        onSuccess={handleSigningSuccess}
        onError={handleSigningError}
        onReject={handleSigningReject}
      />
    </div>
  );
};
