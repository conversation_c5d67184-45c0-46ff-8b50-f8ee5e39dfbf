import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@components/ui/select';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { useBanklinkPaymentMethods } from '@entities/payments';
import { useAppConfig } from '@hooks/system';
import { type FC, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { SupportedCountries } from '@/shared/types';

import { BANKLINK_COUNTRY_OPTIONS } from '../config';

type BanklinkMethodsSelectProps = {
  value: Nullable<string>;
  onChange: (value: Nullable<string>) => void;
};

export const BanklinkMethodsSelect: FC<BanklinkMethodsSelectProps> = ({
  value,
  onChange,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.payment);

  const { country } = useAppConfig();

  useState<Nullable<string>>(null);
  const [selectedCountry, setSelectedCountry] =
    useState<SupportedCountries>(country);

  const { data: paymentMethods, isFetching: isPaymentMethodsFetching } =
    useBanklinkPaymentMethods({
      country: selectedCountry,
    });

  return (
    <div className="grid gap-8">
      <Select
        onValueChange={(value) => {
          setSelectedCountry(value as SupportedCountries);
          onChange(null);
        }}
        value={selectedCountry}
      >
        <SelectTrigger>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {BANKLINK_COUNTRY_OPTIONS.map(({ label, value }) => (
            <SelectItem key={value} value={value}>
              {t(label)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <div className="w-full inline-flex flex-wrap justify-center gap-4">
        {!paymentMethods || isPaymentMethodsFetching
          ? Array.from({ length: 4 }).map((_, index) => (
              <Skeleton
                key={`banklink-method-skeleton-${index.toString()}`}
                className="w-full h-10 rounded-lg min-[375px]:w-[calc(50%-0.5rem)]"
              />
            ))
          : paymentMethods.map((method) => {
              if (!method) return null;

              return (
                <button
                  key={method.key}
                  data-selected={value === method.key}
                  onClick={() => onChange(method.key)}
                  type="button"
                  className="h-10 rounded-lg border border-primary-white bg-primary-white px-[18px] shadow-[0px_3px_10px_0px_rgba(42,40,135,0.05)] transition-colors hover:border-neutral-200 hover:shadow-none data-[selected=true]:border-primary-black data-[selected=true]:shadow-none w-full min-[375px]:w-[calc(50%-0.5rem)] md:py-[3.5px]"
                >
                  <img
                    alt={method.name}
                    src={method.logo_url}
                    className="size-full object-cover"
                  />
                </button>
              );
            })}
      </div>
    </div>
  );
};
