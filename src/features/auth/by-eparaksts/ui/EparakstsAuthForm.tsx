import { LoadingSpinner } from '@components/LoadingSpinner';
import { Button } from '@components/ui/button';
import { LOCIZE_AUTH_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { useNavigate } from '@tanstack/react-router';
import { type FC, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { EparakstsAuthorizationMethod } from '@/shared/types';
import { storeEParakstsOriginalUri } from '@/shared/utils/cookies';

import { eparakstsAuthApi } from '../api';
import { EPARAKSTS_LOGIN_FULL_LINK } from '../config';

const RETURN_URL_BY_EPARAKSTS_AUTH_METHOD: Record<
  EparakstsAuthorizationMethod,
  string
> = {
  [EparakstsAuthorizationMethod.MOBILE]: EPARAKSTS_LOGIN_FULL_LINK,
  [EparakstsAuthorizationMethod.SMARTCARD]: EPARAKSTS_LOGIN_FULL_LINK,
};

type EparakstsAuthFormProps = {
  method: EparakstsAuthorizationMethod;
  onSuccess?: () => Promise<void>;
  onError?: () => void;
  loginCode?: string;
};

export const EparakstsAuthForm: FC<EparakstsAuthFormProps> = ({
  method,
  onSuccess,
  onError,
  loginCode,
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation(LOCIZE_NAMESPACES.auth);

  const {
    mutateAsync: eparakstsLoginChallengeMutation,
    isPending: isEparakstsLoginChallengeMutationLoading,
  } = eparakstsAuthApi.useEparakstsLoginChallengeMutation();

  const { isFetching, data, isFetched, error } =
    eparakstsAuthApi.useEparakstsLoginQuery(
      {
        code: loginCode ?? '',
        return_url: RETURN_URL_BY_EPARAKSTS_AUTH_METHOD[method],
      },
      {
        enabled: !!loginCode,
      },
    );

  const handleFormSubmit = useCallback(async () => {
    try {
      storeEParakstsOriginalUri();

      const data = await eparakstsLoginChallengeMutation({
        method,
        return_url: RETURN_URL_BY_EPARAKSTS_AUTH_METHOD[method],
      });
      if (!data?.eparaksts_login_challenge) {
        throw new Error('No eparaksts_login_challenge data');
      }

      if (!data.eparaksts_login_challenge.redirect_url.startsWith('http')) {
        throw new Error('Invalid eparaksts_login_challenge redirect url');
      }

      window.location.href = data.eparaksts_login_challenge.redirect_url;
    } catch {
      onError?.();
    }
  }, [eparakstsLoginChallengeMutation, onError, method]);

  useEffect(() => {
    if (!isFetched) return;
    if (!data?.eparaksts_login?.is_authenticated) {
      onError?.();
      navigate({
        to: ROUTE_NAMES.current,
        replace: true,
        search: (prev) => ({ ...prev, code: undefined }),
      });
      return;
    }

    onSuccess?.();
  }, [
    isFetched,
    data?.eparaksts_login?.is_authenticated,
    onSuccess,
    navigate,
    onError,
  ]);

  useEffect(() => {
    if (error) {
      onError?.();
      navigate({
        to: ROUTE_NAMES.current,
        replace: true,
        search: (prev) => ({ ...prev, code: undefined }),
      });
    }
  }, [error, onError, navigate]);

  if (isFetching) {
    return (
      <div className="flex w-full flex-col items-center">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="grid w-full max-w-80 gap-14">
      <Button
        fullWidth
        loading={isEparakstsLoginChallengeMutationLoading}
        onClick={handleFormSubmit}
      >
        {t(LOCIZE_AUTH_KEYS.continue)}
      </Button>
    </div>
  );
};
