import { PhoneInput } from '@components/PhoneInput';
import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { Checkbox } from '@components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@components/ui/form';
import { Input } from '@components/ui/input';
import {
  LOCIZE_AUTH_ADDITIONAL_INFO_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { zodResolver } from '@hookform/resolvers/zod';
import { processGqlFormValidationErrors } from '@utils/parseGraphQLError';
import type { ReactNode } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  AdditionalInfoFormSchema,
  type AdditionalInfoFormType,
} from '../schemes';

type AdditionalInfoFormProps = {
  defaultValues?: Partial<AdditionalInfoFormType>;
  after?: ReactNode;
  onSubmit: (data: AdditionalInfoFormType) => Promise<void>;
  isShowNewsLetterAgreement?: boolean;
};

export const AdditionalInfoForm = ({
  defaultValues,
  onSubmit,
  after,
  isShowNewsLetterAgreement = true,
}: AdditionalInfoFormProps) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.authAdditionalInfo);

  const form = useForm<AdditionalInfoFormType>({
    resolver: zodResolver(AdditionalInfoFormSchema),
    defaultValues: {
      phone: '',
      email: '',
      isAcceptedNewsLetterAgreement: false,
      ...defaultValues,
    },
  });

  const handleSubmit = async (data: AdditionalInfoFormType) => {
    try {
      await onSubmit(data);
    } catch (error) {
      if (Array.isArray(error)) {
        processGqlFormValidationErrors({
          error,
          setFormError: form.setError,
        });
      }
    }
  };

  return (
    <Form {...form}>
      <form
        className="mx-auto grid w-full max-w-80 gap-4"
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        <div className="grid w-full gap-1">
          <FormField
            control={form.control}
            name="phone"
            render={({ field: { onChange, ...field } }) => (
              <FormItem className="w-full md:max-w-80">
                <FormControl>
                  <PhoneInput
                    disabled={form.formState.isSubmitting}
                    onValueChange={({ value }) => {
                      onChange(value);
                    }}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="w-full md:max-w-80">
                <FormControl>
                  <Input
                    disabled={form.formState.isSubmitting}
                    placeholder={t(
                      LOCIZE_AUTH_ADDITIONAL_INFO_KEYS.emailPlaceholder,
                    )}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {isShowNewsLetterAgreement && (
            <FormField
              control={form.control}
              name="isAcceptedNewsLetterAgreement"
              render={({ field }) => (
                <FormItem>
                  <div className="flex w-full md:max-w-80">
                    <FormControl>
                      <Checkbox
                        disabled={form.formState.isSubmitting}
                        checked={field.value}
                        className="mt-[.125rem] rounded-md"
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                        }}
                      />
                    </FormControl>
                    <FormLabel className="!mt-0 ml-3 cursor-pointer">
                      <Typography>
                        {t(
                          LOCIZE_AUTH_ADDITIONAL_INFO_KEYS.newsletterAgreementLabel,
                        )}
                      </Typography>
                    </FormLabel>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>
        <Button fullWidth loading={form.formState.isSubmitting} type="submit">
          {t(LOCIZE_AUTH_ADDITIONAL_INFO_KEYS.continueButtonLabel)}
        </Button>
        {after}
      </form>
    </Form>
  );
};
