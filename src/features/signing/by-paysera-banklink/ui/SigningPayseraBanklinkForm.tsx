import { LoadingSpinner } from '@components/LoadingSpinner';
import { Button } from '@components/ui/button';
import { DEFAULT_POLL_INTERVAL } from '@config/common';
import { LOCIZE_COMMON_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useHandleAnalyticsCampaignParameters } from '@entities/analytics';
import { BanklinkMethodsSelect } from '@features/banklink-methods-select';
import { payseraBanklinkSigningApi } from '@features/signing/by-paysera-banklink/api';
import {
  PayseraPaymentStatus,
  PayseraSigningStatus,
} from '@features/signing/by-paysera-banklink/constants';
import { useLocalStorageSessionId } from '@hooks/system/useLocalStorageSessionId';
import { useSearch } from '@tanstack/react-router';
import { type FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useUpdateEffect } from 'react-use';

import type { ContractType, CreditAccount, User } from '@/shared/types';

type SigningPayseraBanklinkFormProps = {
  acceptUrl: string;
  cancelUrl: string;
  userId?: User['id'];
  creditAccountId?: CreditAccount['id'];
  contractType?: ContractType;
  disabled?: boolean;
  onSuccess: () => void;
  onError: (error: unknown) => void;
  onReject: () => void;
  label?: string;
};

export const SigningPayseraBanklinkForm: FC<
  SigningPayseraBanklinkFormProps
> = ({
  userId,
  creditAccountId,
  contractType,
  disabled,
  onSuccess,
  onError,
  onReject,
  acceptUrl,
  cancelUrl,
  label,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.common);
  const { payseraSigningResult } = useSearch({ strict: false });

  const {
    clearParamsFromLocalStorage,
    addParamsFromLocalStorageToUrl,
    setParamsToLocalStorage,
  } = useHandleAnalyticsCampaignParameters();
  const { sessionId, setLocalStorageSessionId } = useLocalStorageSessionId();
  const [selectedPaymentMethodKey, setSelectedPaymentMethodKey] =
    useState<Nullable<string>>(null);
  const [isSuccessfulPayment, setIsSuccessfulPayment] = useState(false);

  const pollPayseraSignatureQuery =
    payseraBanklinkSigningApi.usePollPayseraSignatureQuery(
      {
        sessionId,
      },
      {
        enabled: isSuccessfulPayment && !!sessionId,
        refetchInterval: DEFAULT_POLL_INTERVAL,
      },
    );

  const payseraPaymentStatusQuery =
    payseraBanklinkSigningApi.usePayseraPaymentStatusQuery(undefined, {
      enabled: !!payseraSigningResult,
      refetchInterval: DEFAULT_POLL_INTERVAL,
    });

  const preparePayseraSignatureMutation =
    payseraBanklinkSigningApi.usePreparePayseraSignatureMutation({
      onSuccess: (data) => {
        if (!data?.challenge?.redirect_url || !data?.challenge?.session_id) {
          throw new Error('No data');
        }

        const { session_id, redirect_url } = data.challenge;
        setParamsToLocalStorage();
        setLocalStorageSessionId(session_id);
        window.open(redirect_url, '_self');
      },
      onError,
    });

  const handleFormSubmit = () => {
    if (!selectedPaymentMethodKey)
      throw new Error('No payment method selected');

    preparePayseraSignatureMutation.mutate({
      userId,
      creditAccountId,
      contractType,
      paymentMethodKey: selectedPaymentMethodKey,
      acceptUrl,
      cancelUrl,
    });
  };

  useUpdateEffect(() => {
    if (!payseraPaymentStatusQuery.data) return;

    switch (payseraPaymentStatusQuery.data.status) {
      case PayseraPaymentStatus.FAILED:
      case PayseraPaymentStatus.NOT_FOUND: {
        onReject();
        break;
      }
      default: {
        setIsSuccessfulPayment(true);
        break;
      }
    }
  });

  useUpdateEffect(() => {
    if (!pollPayseraSignatureQuery.data) return;

    addParamsFromLocalStorageToUrl();
    clearParamsFromLocalStorage();

    if (pollPayseraSignatureQuery.data?.success) {
      onSuccess();
      setLocalStorageSessionId('');
      return;
    }

    onReject();
    setLocalStorageSessionId('');
  });

  useUpdateEffect(() => {
    if (pollPayseraSignatureQuery.error)
      onError(pollPayseraSignatureQuery.error);
  });

  useUpdateEffect(() => {
    if (payseraSigningResult === PayseraSigningStatus.REJECT) {
      addParamsFromLocalStorageToUrl();
      clearParamsFromLocalStorage();
      onReject();
    }
  });

  if (pollPayseraSignatureQuery.isFetching)
    return (
      <div className="flex justify-center">
        <LoadingSpinner />
      </div>
    );

  return (
    <div className="grid w-full gap-8">
      {!payseraSigningResult ? (
        <BanklinkMethodsSelect
          value={selectedPaymentMethodKey}
          onChange={setSelectedPaymentMethodKey}
        />
      ) : null}
      <Button
        onClick={handleFormSubmit}
        loading={
          !!payseraSigningResult || preparePayseraSignatureMutation.isPending
        }
        disabled={disabled || !selectedPaymentMethodKey}
      >
        {label || t(LOCIZE_COMMON_KEYS.applyButton)}
      </Button>
    </div>
  );
};
