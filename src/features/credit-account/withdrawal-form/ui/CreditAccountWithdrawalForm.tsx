import { InfoTooltip } from '@components/InfoTooltip';
import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { Form, FormField } from '@components/ui/form';
import { Slider } from '@components/ui/slider';
import { Switch } from '@components/ui/switch';
import { LOCIZE_NAMESPACES, LOCIZE_WITHDRAWAL_KEYS } from '@config/locize';
import { creditAccountWithdrawalApi } from '@entities/credit-account-withdrawal';
import { InstantPaymentStatus } from '@features/credit-account/withdrawal-form/types';
import { zodResolver } from '@hookform/resolvers/zod';
import MinusIcon from '@icons/caw-buttons/minus.svg?react';
import PlusIcon from '@icons/caw-buttons/plus.svg?react';
import { keepPreviousData } from '@tanstack/react-query';
import { type FC, type FocusEventHandler, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Trans, useTranslation } from 'react-i18next';
import { NumericFormat } from 'react-number-format';
import { useDebounce, useMount, useUpdateEffect } from 'react-use';

import { cn } from '@/shared/utils/tailwind';

import {
  CreditAccountWithdrawalFormSchema,
  type CreditAccountWithdrawalFormType,
} from '../schema';
import { CreditAccountWithdrawalFormTitle } from './CreditAccountWithdrawalFormTitle';

type CreditAccountWithdrawalFormProps = {
  creditAccountId: number;
  minWithdrawalAmount: number;
  minInstantWithdrawalAmount: number;
  maxWithdrawalAmount: number;
  step?: number;
  defaultValues?: Nullable<CreditAccountWithdrawalFormType>;
  onSubmit: (v: CreditAccountWithdrawalFormType) => void;
  instantPaymentStatus: InstantPaymentStatus;
  instantPaymentFee: number;
};

export const CreditAccountWithdrawalForm: FC<
  CreditAccountWithdrawalFormProps
> = ({
  creditAccountId,
  minWithdrawalAmount,
  minInstantWithdrawalAmount,
  maxWithdrawalAmount,
  onSubmit,
  defaultValues,
  instantPaymentFee,
  instantPaymentStatus,
  step = minWithdrawalAmount,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.withdrawal);

  const [debouncedAmount, setDebouncedAmount] = useState(0);
  const [isAmountChanged, setIsAmountChanged] = useState(false);

  const maxAmount = useMemo(
    () => maxWithdrawalAmount - (maxWithdrawalAmount % step),
    [maxWithdrawalAmount, step],
  );
  const validationSchema = useMemo(
    () =>
      CreditAccountWithdrawalFormSchema({
        maxWithdrawalAmount: maxAmount,
        minWithdrawalAmount,
        minInstantWithdrawalAmount,
      }),
    [minWithdrawalAmount, minInstantWithdrawalAmount, maxAmount],
  );

  const form = useForm<CreditAccountWithdrawalFormType>({
    resolver: zodResolver(validationSchema),
    mode: 'onChange',
    defaultValues: {
      amount: 0,
      isInstantPayment: false,
    },
  });
  const [amount, isInstantPayment] = form.watch(['amount', 'isInstantPayment']);

  const { data, isFetching } =
    creditAccountWithdrawalApi.useCreditAccountNextInvoiceQuery(
      {
        creditAccountId,
        additionalAmount: debouncedAmount,
      },
      {
        enabled: !!debouncedAmount,
        placeholderData: keepPreviousData,
      },
    );

  const isInstantPaymentFeatureEnabled = useMemo(
    () => InstantPaymentStatus.ENABLED === instantPaymentStatus,
    [instantPaymentStatus],
  );

  const handleAmountButtonsClick = (operation: 'inc' | 'dec') => () => {
    const amountValue = amount ?? 0;
    const newValue =
      operation === 'inc' ? amountValue + step : amountValue - step;
    form.setValue('amount', newValue);
  };

  const handleAmountInputBlur = () => {
    if (!amount) {
      return;
    }

    if (amount < minWithdrawalAmount) {
      form.setValue('amount', minWithdrawalAmount);
      return;
    }

    if (amount > maxAmount) {
      form.setValue('amount', maxAmount);
      return;
    }

    if (amount % step !== 0) {
      form.setValue('amount', +(Math.ceil(amount / step) * step).toFixed(0));
      return;
    }
  };

  const handleFocus: FocusEventHandler<HTMLInputElement> = (event) => {
    const inputEl = event.target;
    inputEl.selectionStart = inputEl.selectionEnd = inputEl.value.length;
  };

  useDebounce(
    () => {
      setDebouncedAmount(amount);
      setIsAmountChanged(false);
    },
    400,
    [amount],
  );

  useUpdateEffect(() => {
    if (!isAmountChanged && amount !== debouncedAmount) {
      setIsAmountChanged(true);
    }
  });

  useMount(() => {
    if (defaultValues) {
      form.setValue('amount', defaultValues?.amount);
      form.setValue('isInstantPayment', defaultValues?.isInstantPayment);

      return;
    }

    form.setValue(
      'amount',
      +(Math.ceil(maxAmount / 2 / step) * step).toFixed(0),
    );
    form.setValue(
      'isInstantPayment',
      instantPaymentStatus !== InstantPaymentStatus.DISABLED,
    );
  });

  return (
    <Form {...form}>
      <form className="w-full" onSubmit={form.handleSubmit(onSubmit)}>
        <div className="flex flex-col justify-center">
          <CreditAccountWithdrawalFormTitle />
          <div
            className={cn('py-12', isInstantPaymentFeatureEnabled && 'pb-4')}
          >
            <FormField
              control={form.control}
              name="amount"
              render={({ field: { onChange, value, name, ...field } }) => (
                <>
                  <div className="flex justify-between items-center">
                    <button
                      type="button"
                      className="size-10 rounded-[1rem] bg-neutral-100 h-full flex items-center justify-center transition-colors hover:bg-neutral-200"
                      onClick={handleAmountButtonsClick('dec')}
                      disabled={value <= minWithdrawalAmount}
                    >
                      <MinusIcon />
                    </button>
                    <NumericFormat
                      {...field}
                      onFocus={handleFocus}
                      allowLeadingZeros={false}
                      allowNegative={false}
                      className="text-[3.5rem] leading-[3.5rem] tracking-[-.21rem] font-bold border-none bg-primary-white text-center focus-visible:border-none p-0 h-16 flex-1 w-full focus-visible:outline-none"
                      fixedDecimalScale
                      name={`${name}-input`}
                      onValueChange={(v) => {
                        onChange(v.floatValue);
                      }}
                      suffix=" €"
                      onBlur={handleAmountInputBlur}
                      thousandSeparator=","
                      value={value}
                    />
                    <button
                      type="button"
                      className="size-10 rounded-[1rem] bg-neutral-100 h-full flex items-center justify-center transition-colors hover:bg-neutral-200"
                      onClick={handleAmountButtonsClick('inc')}
                      disabled={value >= maxAmount}
                    >
                      <PlusIcon />
                    </button>
                  </div>
                  <Slider
                    {...field}
                    className="mt-6"
                    max={maxAmount}
                    min={minWithdrawalAmount}
                    name={`${name}-slider`}
                    onValueChange={(value) => {
                      onChange(+value[0].toFixed(0));
                    }}
                    step={step ?? minWithdrawalAmount}
                    value={[value]}
                  />
                </>
              )}
            />
          </div>
          {isInstantPaymentFeatureEnabled && (
            <div className="grid grid-cols-[1fr_auto] items-center gap-x-2 gap-y-1 pt-4 pb-2">
              <div className="flex items-center gap-2">
                <Typography variant="text-l">
                  {t(LOCIZE_WITHDRAWAL_KEYS.formInstantPaymentLabel)}
                </Typography>
                <InfoTooltip
                  text={t(LOCIZE_WITHDRAWAL_KEYS.formInstantPaymentTooltip, {
                    fee: instantPaymentFee.toFixed(2),
                  })}
                />
              </div>
              <FormField
                control={form.control}
                name="isInstantPayment"
                render={({ field: { onChange, value, ...field } }) => (
                  <Switch
                    {...field}
                    checked={value}
                    onCheckedChange={(state) => {
                      onChange(state);
                      form.setValue('amount', amount, {
                        shouldValidate: true,
                      });
                    }}
                  />
                )}
              />
              <Typography
                className={cn(
                  'col-span-2 text-neutral-400 transition-opacity opacity-100',
                  isInstantPayment && '!opacity-0',
                )}
                variant="text-s"
              >
                {t(LOCIZE_WITHDRAWAL_KEYS.formInstantPaymentDescription)}
              </Typography>
            </div>
          )}
        </div>
        <div className="mt-10">
          {form.formState.errors.amount?.message ? (
            <Typography
              affects="semibold"
              className="text-center text-system-red"
              variant="text-s"
            >
              <Trans
                i18nKey={form.formState.errors.amount.message}
                values={{
                  amount: `${minInstantWithdrawalAmount} €`,
                }}
                components={{
                  br: <br />,
                }}
                t={t}
              />
            </Typography>
          ) : (
            <Typography
              affects="semibold"
              className={cn(
                'text-center text-neutral-500',
                amount === undefined && 'opacity-0',
              )}
              variant="text-s"
            >
              {t(LOCIZE_WITHDRAWAL_KEYS.formMonthlyPaymentLabel, {
                amount: data?.credit_account_next_invoice?.total ?? '...',
              })}
            </Typography>
          )}
          <Button
            className="mt-6 w-full"
            disabled={!form.formState.isValid}
            loading={isFetching || isAmountChanged}
            type="submit"
          >
            {t(LOCIZE_WITHDRAWAL_KEYS.formSubmitButton)}
          </Button>
        </div>
      </form>
    </Form>
  );
};
